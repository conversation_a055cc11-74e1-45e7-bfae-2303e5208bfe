<?php
/**
 * Direct test of SETIA settings page
 */

// WordPress environment
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

if (!current_user_can('manage_options')) {
    die('Access denied');
}

// Force clear cache
wp_cache_flush();
update_option('setia_asset_version', time());

?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SETIA Direct Test</title>
    
    <!-- Force load CSS -->
    <link rel="stylesheet" href="assets/css/admin-settings.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="assets/css/admin.css?v=<?php echo time(); ?>">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        body {
            margin: 20px;
            font-family: 'IRANSans', Tahoma, sans-serif;
            direction: rtl;
        }
        .test-info {
            background: #f0f8ff;
            border: 1px solid #0078d4;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h2>🧪 SETIA Direct Test</h2>
        <p>این صفحه مستقیماً فایل‌های CSS و JS را لود می‌کند</p>
        <p>زمان: <?php echo date('Y-m-d H:i:s'); ?></p>
        <p>Asset Version: <?php echo get_option('setia_asset_version', 'not set'); ?></p>
    </div>

    <?php
    // Include the actual settings page
    include 'templates/settings-page.php';
    ?>

    <!-- Force load JS -->
    <script src="assets/js/settings-enhanced.js?v=<?php echo time(); ?>"></script>
    
    <script>
        console.log('🚀 Direct test page loaded');
        console.log('jQuery version:', jQuery.fn.jquery);
        
        jQuery(document).ready(function($) {
            console.log('📱 Direct test ready');
            console.log('Tab buttons found:', $('.setia-tab-button').length);
            console.log('Tab panes found:', $('.setia-tab-pane').length);
        });
    </script>
</body>
</html>
