/**
 * SETIA Advanced History Page JavaScript
 * Modern, Interactive, RTL-Compatible Functionality
 * 
 * @package SETIA
 * @version 2.0
 */

(function($) {
    'use strict';

    console.log('SETIA History JS: Loading history-advanced.js');

    // Global history manager object
    window.SetiaHistoryManager = {

        // Initialize the history page
        init: function() {
            console.log('SETIA History JS: Initializing SetiaHistoryManager');
            console.log('SETIA History JS: setiaHistory config:', window.setiaHistory);

            this.bindEvents();
            this.loadStats();
            this.loadHistoryData();
            this.initializeDatePickers();
            this.setupTableSorting();
        },

        // Bind all event handlers
        bindEvents: function() {
            // Header actions
            $('#refresh-data').on('click', this.refreshData.bind(this));
            $('#export-excel').on('click', this.exportExcel.bind(this));
            
            // Filter actions
            $('#toggle-filters').on('click', this.toggleFilters.bind(this));
            $('#apply-filters').on('click', this.applyFilters.bind(this));
            $('#reset-filters').on('click', this.resetFilters.bind(this));
            
            // Table actions
            $('#select-all').on('change', this.toggleSelectAll.bind(this));
            $(document).on('change', '.content-checkbox', this.updateBulkActions.bind(this));
            $('#apply-bulk-action').on('click', this.applyBulkAction.bind(this));
            $('#items-per-page').on('change', this.changeItemsPerPage.bind(this));
            
            // Content actions
            $(document).on('click', '.action-view', this.viewContent.bind(this));
            $(document).on('click', '.action-edit', this.editContent.bind(this));
            $(document).on('click', '.action-publish', this.publishContent.bind(this));
            $(document).on('click', '.action-draft', this.saveDraft.bind(this));
            $(document).on('click', '.action-delete', this.deleteContent.bind(this));
            
            // Modal actions
            $('#close-modal, #modal-close-btn, .setia-modal-overlay').on('click', this.closeModal.bind(this));
            $('#close-edit-modal, #cancel-edit-btn').on('click', this.closeEditModal.bind(this));
            $('#save-content-btn').on('click', this.saveContentChanges.bind(this));
            $('#modal-edit-btn').on('click', this.openWordPressEdit.bind(this));
            
            // Confirmation modal
            $('#confirm-yes').on('click', this.confirmAction.bind(this));
            $('#confirm-no').on('click', this.closeConfirmation.bind(this));
            
            // Pagination
            $(document).on('click', '.pagination-btn', this.changePage.bind(this));
            
            // Search on enter
            $('#keyword-search').on('keypress', function(e) {
                if (e.which === 13) {
                    SetiaHistoryManager.applyFilters();
                }
            });
        },

        // Load statistics
        loadStats: function() {
            console.log('SETIA History JS: Loading stats...');
            console.log('SETIA History JS: AJAX URL:', window.setiaHistory.ajaxUrl);
            console.log('SETIA History JS: Nonce:', window.setiaHistory.nonce);

            $.ajax({
                url: window.setiaHistory.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'setia_get_history_stats',
                    nonce: window.setiaHistory.nonce
                },
                success: function(response) {
                    console.log('SETIA History JS: Stats response:', response);
                    if (response.success) {
                        SetiaHistoryManager.updateStats(response.data);
                    } else {
                        console.error('SETIA History JS: Stats error:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('SETIA History JS: Stats AJAX error:', xhr.responseText);
                    SetiaHistoryManager.showNotification('خطا در بارگذاری آمار', 'error');
                }
            });
        },

        // Update statistics display
        updateStats: function(stats) {
            $('#total-content').text(stats.total_content);
            $('#published-content').text(stats.published_content);
            $('#draft-content').text(stats.draft_content);
            $('#product-content').text(stats.product_content);
            
            // Update change indicators
            $('.setia-stat-card[data-stat="total"] .setia-stat-change').text('+' + stats.today_content + ' امروز');
            $('.setia-stat-card[data-stat="published"] .setia-stat-change').text(stats.publish_rate + '% نرخ انتشار');
        },

        // Load history data with filters and pagination
        loadHistoryData: function() {
            console.log('SETIA History JS: Loading history data...');
            $('#loading-state').show();
            $('#table-container, #empty-state, #pagination-container').hide();

            const filters = this.getFilters();
            console.log('SETIA History JS: Filters:', filters);

            const requestData = {
                action: 'setia_get_history_data',
                nonce: window.setiaHistory.nonce,
                page: window.setiaHistory.currentPage,
                per_page: window.setiaHistory.itemsPerPage,
                sort_by: window.setiaHistory.sortBy,
                sort_order: window.setiaHistory.sortOrder,
                filters: filters
            };
            console.log('SETIA History JS: Request data:', requestData);

            $.ajax({
                url: window.setiaHistory.ajaxUrl,
                type: 'POST',
                data: requestData,
                success: function(response) {
                    console.log('SETIA History JS: Data response:', response);
                    $('#loading-state').hide();

                    if (response.success) {
                        SetiaHistoryManager.renderTable(response.data);
                        SetiaHistoryManager.renderPagination(response.data);
                        window.setiaHistory.totalItems = response.data.total_items;
                    } else {
                        console.error('SETIA History JS: Data error:', response);
                        SetiaHistoryManager.showNotification(response.data.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('SETIA History JS: Data AJAX error:', xhr.responseText);
                    $('#loading-state').hide();
                    SetiaHistoryManager.showNotification('خطا در بارگذاری داده‌ها', 'error');
                }
            });
        },

        // Get current filters
        getFilters: function() {
            return {
                date_from: $('#date-range-from').val(),
                date_to: $('#date-range-to').val(),
                content_type: $('#content-type').val(),
                status: $('#status-filter').val(),
                category: $('#category-filter').val(),
                keyword: $('#keyword-search').val(),
                word_count_min: $('#word-count-min').val(),
                word_count_max: $('#word-count-max').val()
            };
        },

        // Render table with data
        renderTable: function(data) {
            const tbody = $('#content-table-body');
            tbody.empty();
            
            if (data.items.length === 0) {
                $('#empty-state').show();
                $('#table-container').hide();
                $('#results-count').text('0 مورد یافت شد');
                return;
            }
            
            $('#table-container').show();
            $('#empty-state').hide();
            $('#results-count').text(data.total_items + ' مورد یافت شد');
            
            data.items.forEach(function(item) {
                const row = SetiaHistoryManager.createTableRow(item);
                tbody.append(row);
            });
            
            // Update select all checkbox
            $('#select-all').prop('checked', false);
            this.updateBulkActions();
        },

        // Create table row HTML
        createTableRow: function(item) {
            const statusClass = 'setia-status-' + item.status;
            const statusText = item.status === 'published' ? 'منتشر شده' : 'پیش‌نویس';
            
            return `
                <tr data-id="${item.id}">
                    <td class="setia-checkbox-col">
                        <input type="checkbox" class="content-checkbox setia-checkbox" value="${item.id}">
                    </td>
                    <td>
                        <strong>${item.title}</strong>
                        <div style="font-size: 0.75rem; color: #666; margin-top: 0.25rem;">
                            ${item.excerpt}
                        </div>
                    </td>
                    <td>
                        <span class="setia-status-badge">
                            ${item.content_type === 'post' ? 'مقاله' : item.content_type === 'product' ? 'محصول' : 'صفحه'}
                        </span>
                    </td>
                    <td>
                        <span class="setia-status-badge ${statusClass}">
                            ${statusText}
                        </span>
                    </td>
                    <td>${item.created_at_persian}</td>
                    <td>${item.primary_keyword || '-'}</td>
                    <td>${item.word_count} کلمه</td>
                    <td class="setia-actions-col">
                        <div class="setia-action-buttons">
                            <button class="setia-action-btn setia-action-view action-view" data-id="${item.id}" title="مشاهده">
                                <i class="dashicons dashicons-visibility"></i>
                            </button>
                            <button class="setia-action-btn setia-action-edit action-edit" data-id="${item.id}" title="ویرایش">
                                <i class="dashicons dashicons-edit"></i>
                            </button>
                            ${item.status === 'draft' ? `
                                <button class="setia-action-btn setia-action-publish action-publish" data-id="${item.id}" title="انتشار">
                                    <i class="dashicons dashicons-yes"></i>
                                </button>
                                <button class="setia-action-btn setia-action-draft action-draft" data-id="${item.id}" title="ذخیره به عنوان پیش‌نویس">
                                    <i class="dashicons dashicons-media-document"></i>
                                </button>
                            ` : `
                                <a href="${item.edit_url}" class="setia-action-btn setia-action-edit" title="ویرایش در وردپرس" target="_blank">
                                    <i class="dashicons dashicons-external"></i>
                                </a>
                            `}
                            <button class="setia-action-btn setia-action-delete action-delete" data-id="${item.id}" title="حذف">
                                <i class="dashicons dashicons-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        },

        // Render pagination
        renderPagination: function(data) {
            if (data.total_pages <= 1) {
                $('#pagination-container').hide();
                return;
            }
            
            $('#pagination-container').show();
            
            const start = ((data.current_page - 1) * data.per_page) + 1;
            const end = Math.min(data.current_page * data.per_page, data.total_items);
            
            $('#pagination-info').text(`نمایش ${start} تا ${end} از ${data.total_items} مورد`);
            
            const controls = $('#pagination-controls');
            controls.empty();
            
            // Previous button
            if (data.current_page > 1) {
                controls.append(`<button class="pagination-btn" data-page="${data.current_page - 1}">قبلی</button>`);
            }
            
            // Page numbers
            const startPage = Math.max(1, data.current_page - 2);
            const endPage = Math.min(data.total_pages, data.current_page + 2);
            
            if (startPage > 1) {
                controls.append(`<button class="pagination-btn" data-page="1">1</button>`);
                if (startPage > 2) {
                    controls.append(`<span>...</span>`);
                }
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === data.current_page ? 'active' : '';
                controls.append(`<button class="pagination-btn ${activeClass}" data-page="${i}">${i}</button>`);
            }
            
            if (endPage < data.total_pages) {
                if (endPage < data.total_pages - 1) {
                    controls.append(`<span>...</span>`);
                }
                controls.append(`<button class="pagination-btn" data-page="${data.total_pages}">${data.total_pages}</button>`);
            }
            
            // Next button
            if (data.current_page < data.total_pages) {
                controls.append(`<button class="pagination-btn" data-page="${data.current_page + 1}">بعدی</button>`);
            }
        },

        // Show notification
        showNotification: function(message, type = 'success') {
            // Create notification element
            const notification = $(`
                <div class="setia-notification setia-notification-${type}">
                    <span>${message}</span>
                    <button class="setia-notification-close">&times;</button>
                </div>
            `);

            // Add to page
            $('body').append(notification);

            // Auto remove after 5 seconds
            setTimeout(function() {
                notification.fadeOut(function() {
                    notification.remove();
                });
            }, 5000);

            // Manual close
            notification.find('.setia-notification-close').on('click', function() {
                notification.fadeOut(function() {
                    notification.remove();
                });
            });
        },

        // Toggle filters panel
        toggleFilters: function() {
            $('#filters-content').slideToggle();
        },

        // Apply filters
        applyFilters: function() {
            window.setiaHistory.currentPage = 1;
            window.setiaHistory.currentFilters = this.getFilters();
            this.loadHistoryData();
        },

        // Reset filters
        resetFilters: function() {
            $('#date-range-from, #date-range-to, #keyword-search, #word-count-min, #word-count-max').val('');
            $('#content-type, #status-filter, #category-filter').val('');
            this.applyFilters();
        },

        // Refresh data
        refreshData: function() {
            this.loadStats();
            this.loadHistoryData();
            this.showNotification('داده‌ها بروزرسانی شد');
        },

        // Toggle select all
        toggleSelectAll: function() {
            const isChecked = $('#select-all').is(':checked');
            $('.content-checkbox').prop('checked', isChecked);
            this.updateBulkActions();
        },

        // Update bulk actions
        updateBulkActions: function() {
            const selectedCount = $('.content-checkbox:checked').length;
            $('#apply-bulk-action').prop('disabled', selectedCount === 0);

            // Update selected items array
            window.setiaHistory.selectedItems = [];
            $('.content-checkbox:checked').each(function() {
                window.setiaHistory.selectedItems.push($(this).val());
            });
        },

        // Apply bulk action
        applyBulkAction: function() {
            const action = $('#bulk-action').val();
            const selectedIds = window.setiaHistory.selectedItems;

            if (!action || selectedIds.length === 0) {
                this.showNotification('لطفاً عملیات و آیتم‌های مورد نظر را انتخاب کنید', 'error');
                return;
            }

            let message = '';
            switch (action) {
                case 'delete':
                    message = `آیا از حذف ${selectedIds.length} مورد انتخاب شده اطمینان دارید؟`;
                    break;
                case 'publish':
                    message = `آیا از انتشار ${selectedIds.length} مورد انتخاب شده اطمینان دارید؟`;
                    break;
                case 'draft':
                    message = `آیا از تبدیل ${selectedIds.length} مورد انتخاب شده به پیش‌نویس اطمینان دارید؟`;
                    break;
            }

            this.showConfirmation(message, function() {
                SetiaHistoryManager.executeBulkAction(action, selectedIds);
            });
        },

        // Execute bulk action
        executeBulkAction: function(action, contentIds) {
            $.ajax({
                url: window.setiaHistory.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'setia_bulk_action_content',
                    nonce: window.setiaHistory.nonce,
                    action_type: action,
                    content_ids: contentIds
                },
                success: function(response) {
                    if (response.success) {
                        SetiaHistoryManager.showNotification(response.data.message);
                        SetiaHistoryManager.loadHistoryData();
                        SetiaHistoryManager.loadStats();
                        $('#bulk-action').val('');
                    } else {
                        SetiaHistoryManager.showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    SetiaHistoryManager.showNotification('خطا در انجام عملیات', 'error');
                }
            });
        },

        // Change items per page
        changeItemsPerPage: function() {
            window.setiaHistory.itemsPerPage = parseInt($('#items-per-page').val());
            window.setiaHistory.currentPage = 1;
            this.loadHistoryData();
        },

        // Change page
        changePage: function(e) {
            const page = parseInt($(e.target).data('page'));
            if (page && page !== window.setiaHistory.currentPage) {
                window.setiaHistory.currentPage = page;
                this.loadHistoryData();
            }
        },

        // View content
        viewContent: function(e) {
            const contentId = $(e.target).closest('.action-view').data('id');

            $.ajax({
                url: window.setiaHistory.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'setia_get_content_preview',
                    nonce: window.setiaHistory.nonce,
                    content_id: contentId
                },
                success: function(response) {
                    if (response.success) {
                        SetiaHistoryManager.showContentPreview(response.data);
                    } else {
                        SetiaHistoryManager.showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    SetiaHistoryManager.showNotification('خطا در بارگذاری محتوا', 'error');
                }
            });
        },

        // Show content preview modal
        showContentPreview: function(content) {
            $('#modal-title').text('پیش‌نمایش: ' + content.title);
            $('#modal-content').html(`
                <div class="setia-content-preview">
                    <div class="setia-content-meta">
                        <p><strong>نوع محتوا:</strong> ${content.content_type}</p>
                        <p><strong>کلیدواژه اصلی:</strong> ${content.primary_keyword || 'ندارد'}</p>
                        <p><strong>تعداد کلمات:</strong> ${content.word_count}</p>
                        <p><strong>تاریخ تولید:</strong> ${content.created_at_persian}</p>
                    </div>
                    <div class="setia-content-body">
                        <h3>محتوا:</h3>
                        <div class="setia-content-text">${content.content}</div>
                    </div>
                </div>
            `);

            if (content.edit_url) {
                $('#modal-edit-btn').show().data('url', content.edit_url);
            } else {
                $('#modal-edit-btn').hide();
            }

            $('#content-preview-modal').fadeIn();
        },

        // Edit content
        editContent: function(e) {
            const contentId = $(e.target).closest('.action-edit').data('id');

            $.ajax({
                url: window.setiaHistory.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'setia_edit_content',
                    nonce: window.setiaHistory.nonce,
                    content_id: contentId
                },
                success: function(response) {
                    if (response.success) {
                        SetiaHistoryManager.showEditModal(response.data);
                    } else {
                        SetiaHistoryManager.showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    SetiaHistoryManager.showNotification('خطا در بارگذاری محتوا', 'error');
                }
            });
        },

        // Show edit modal
        showEditModal: function(data) {
            $('#edit-content-id').val(data.content.id);
            $('#edit-title').val(data.content.title);
            $('#edit-content').val(data.content.content);
            $('#edit-keyword').val(data.content.primary_keyword);

            // Populate categories
            const categorySelect = $('#edit-category');
            categorySelect.empty().append('<option value="">انتخاب دسته‌بندی</option>');
            data.categories.forEach(function(category) {
                categorySelect.append(`<option value="${category.id}">${category.name}</option>`);
            });

            $('#content-edit-modal').fadeIn();
        },

        // Save content changes
        saveContentChanges: function() {
            const formData = {
                action: 'setia_update_content',
                nonce: window.setiaHistory.nonce,
                content_id: $('#edit-content-id').val(),
                title: $('#edit-title').val(),
                content: $('#edit-content').val(),
                keyword: $('#edit-keyword').val(),
                category: $('#edit-category').val(),
                tags: $('#edit-tags').val()
            };

            $.ajax({
                url: window.setiaHistory.ajaxUrl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        SetiaHistoryManager.showNotification(response.data.message);
                        SetiaHistoryManager.closeEditModal();
                        SetiaHistoryManager.loadHistoryData();
                    } else {
                        SetiaHistoryManager.showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    SetiaHistoryManager.showNotification('خطا در ذخیره تغییرات', 'error');
                }
            });
        },

        // Publish content
        publishContent: function(e) {
            const contentId = $(e.target).closest('.action-publish').data('id');

            this.showConfirmation('آیا از انتشار این محتوا اطمینان دارید؟', function() {
                $.ajax({
                    url: window.setiaHistory.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'setia_publish_content_item',
                        nonce: window.setiaHistory.nonce,
                        content_id: contentId
                    },
                    success: function(response) {
                        if (response.success) {
                            SetiaHistoryManager.showNotification(response.data.message);
                            SetiaHistoryManager.loadHistoryData();
                            SetiaHistoryManager.loadStats();
                        } else {
                            SetiaHistoryManager.showNotification(response.data.message, 'error');
                        }
                    },
                    error: function() {
                        SetiaHistoryManager.showNotification('خطا در انتشار محتوا', 'error');
                    }
                });
            });
        },

        // Save as draft
        saveDraft: function(e) {
            const contentId = $(e.target).closest('.action-draft').data('id');

            $.ajax({
                url: window.setiaHistory.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'setia_save_as_draft',
                    nonce: window.setiaHistory.nonce,
                    content_id: contentId
                },
                success: function(response) {
                    if (response.success) {
                        SetiaHistoryManager.showNotification(response.data.message);
                        SetiaHistoryManager.loadHistoryData();
                        SetiaHistoryManager.loadStats();
                    } else {
                        SetiaHistoryManager.showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    SetiaHistoryManager.showNotification('خطا در ذخیره پیش‌نویس', 'error');
                }
            });
        },

        // Delete content
        deleteContent: function(e) {
            const contentId = $(e.target).closest('.action-delete').data('id');

            this.showConfirmation('آیا از حذف این محتوا اطمینان دارید؟ این عمل قابل بازگشت نیست.', function() {
                $.ajax({
                    url: window.setiaHistory.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'setia_delete_content_item',
                        nonce: window.setiaHistory.nonce,
                        content_id: contentId
                    },
                    success: function(response) {
                        if (response.success) {
                            SetiaHistoryManager.showNotification(response.data.message);
                            SetiaHistoryManager.loadHistoryData();
                            SetiaHistoryManager.loadStats();
                        } else {
                            SetiaHistoryManager.showNotification(response.data.message, 'error');
                        }
                    },
                    error: function() {
                        SetiaHistoryManager.showNotification('خطا در حذف محتوا', 'error');
                    }
                });
            });
        },

        // Export to Excel
        exportExcel: function() {
            $.ajax({
                url: window.setiaHistory.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'setia_export_history_excel',
                    nonce: window.setiaHistory.nonce
                },
                success: function(response) {
                    if (response.success) {
                        SetiaHistoryManager.showNotification(response.data.message);
                        // Download file
                        const link = document.createElement('a');
                        link.href = response.data.download_url;
                        link.download = response.data.filename;
                        link.click();
                    } else {
                        SetiaHistoryManager.showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    SetiaHistoryManager.showNotification('خطا در ایجاد فایل Excel', 'error');
                }
            });
        },

        // Initialize date pickers
        initializeDatePickers: function() {
            // Persian date picker initialization
            if (typeof $.fn.persianDatepicker !== 'undefined') {
                $('.persian-datepicker').persianDatepicker({
                    format: 'YYYY/MM/DD',
                    calendar: {
                        persian: {
                            locale: 'fa'
                        }
                    }
                });
            }
        },

        // Setup table sorting
        setupTableSorting: function() {
            $('.setia-sortable').on('click', function() {
                const sortBy = $(this).data('sort');

                if (window.setiaHistory.sortBy === sortBy) {
                    window.setiaHistory.sortOrder = window.setiaHistory.sortOrder === 'asc' ? 'desc' : 'asc';
                } else {
                    window.setiaHistory.sortBy = sortBy;
                    window.setiaHistory.sortOrder = 'desc';
                }

                // Update UI
                $('.setia-sortable').removeClass('sorted');
                $(this).addClass('sorted');

                SetiaHistoryManager.loadHistoryData();
            });
        },

        // Show confirmation modal
        showConfirmation: function(message, callback) {
            $('#confirm-message').text(message);
            $('#confirmation-modal').fadeIn();

            // Store callback for later use
            this.confirmCallback = callback;
        },

        // Confirm action
        confirmAction: function() {
            if (this.confirmCallback) {
                this.confirmCallback();
                this.confirmCallback = null;
            }
            this.closeConfirmation();
        },

        // Close confirmation modal
        closeConfirmation: function() {
            $('#confirmation-modal').fadeOut();
            this.confirmCallback = null;
        },

        // Close modal
        closeModal: function(e) {
            if (e.target === e.currentTarget || $(e.target).hasClass('setia-modal-close') || $(e.target).closest('.setia-modal-close').length) {
                $('#content-preview-modal').fadeOut();
            }
        },

        // Close edit modal
        closeEditModal: function(e) {
            if (!e || e.target === e.currentTarget || $(e.target).hasClass('setia-modal-close') || $(e.target).closest('.setia-modal-close').length) {
                $('#content-edit-modal').fadeOut();
            }
        },

        // Open WordPress edit
        openWordPressEdit: function() {
            const url = $('#modal-edit-btn').data('url');
            if (url) {
                window.open(url, '_blank');
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('SETIA History JS: Document ready');
        console.log('SETIA History JS: Looking for .setia-history-advanced container');
        console.log('SETIA History JS: Container found:', $('.setia-history-advanced').length);

        if ($('.setia-history-advanced').length) {
            console.log('SETIA History JS: Container found, initializing...');
            SetiaHistoryManager.init();
        } else {
            console.log('SETIA History JS: Container not found, checking for other selectors...');
            console.log('SETIA History JS: .wrap elements:', $('.wrap').length);
            console.log('SETIA History JS: Page elements with setia:', $('[class*="setia"]').length);

            // Try alternative initialization
            if ($('#setia-history-page').length || $('.setia-stats-grid').length) {
                console.log('SETIA History JS: Alternative container found, initializing...');
                SetiaHistoryManager.init();
            }
        }
    });

})(jQuery);

console.log('SETIA History JS: File loaded completely');
