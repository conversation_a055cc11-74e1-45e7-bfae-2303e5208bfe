<?php
/**
 * Test Hook Fix for SETIA Settings
 */

// WordPress environment
if (file_exists('../../../wp-config.php')) {
    require_once('../../../wp-config.php');
    require_once('../../../wp-load.php');
} else {
    die('WordPress not found');
}

if (!current_user_can('manage_options')) {
    die('Access denied');
}

// Force clear cache
wp_cache_flush();
update_option('setia_asset_version', time());

?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SETIA Hook Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f1f1f1;
        }
        .test-box {
            background: white;
            border: 1px solid #ccd0d4;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .success {
            background: #d1fae5;
            border-color: #10b981;
            color: #065f46;
        }
        .error {
            background: #fee2e2;
            border-color: #ef4444;
            color: #991b1b;
        }
        .info {
            background: #e7f3ff;
            border-color: #0073aa;
            color: #0c4a6e;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 SETIA Hook Fix Test</h1>
    
    <div class="test-box info">
        <h2>📊 Hook Analysis</h2>
        <?php
        // Simulate the problematic hook
        $original_hook = '%d8%aa%d9%88%d9%84%db%8c%d8%af-%d9%85%d8%ad%d8%aa%d9%88%d8%a7-%d8%a8%d8%a7-%d9%87%d9%88%d8%b4-%d9%85%d8%b5%d9%86%d9%88%d8%b9%db%8c_page_setia-settings';
        $decoded_hook = urldecode($original_hook);
        
        echo "<p><strong>Original Hook:</strong></p>";
        echo "<div class='code'>" . esc_html($original_hook) . "</div>";
        
        echo "<p><strong>Decoded Hook:</strong></p>";
        echo "<div class='code'>" . esc_html($decoded_hook) . "</div>";
        
        // Test conditions
        $conditions = [
            'Exact match (old)' => ($original_hook == 'setia-content-generator_page_setia-settings'),
            'Exact match (new)' => ($decoded_hook == 'setia-content-generator_page_setia-settings'),
            'Contains setia-settings (old)' => (strpos($original_hook, 'setia-settings') !== false),
            'Contains setia-settings (new)' => (strpos($decoded_hook, 'setia-settings') !== false),
            'Contains page_setia-settings (old)' => (strpos($original_hook, 'page_setia-settings') !== false),
            'Contains page_setia-settings (new)' => (strpos($decoded_hook, 'page_setia-settings') !== false),
            'Contains Persian name (new)' => (strpos($decoded_hook, 'تولید-محتوا-با-هوش-مصنوعی_page_setia-settings') !== false),
        ];
        
        echo "<h3>🧪 Condition Tests:</h3>";
        foreach ($conditions as $test => $result) {
            $class = $result ? 'success' : 'error';
            $icon = $result ? '✅' : '❌';
            echo "<p class='{$class}'>{$icon} {$test}: " . ($result ? 'PASS' : 'FAIL') . "</p>";
        }
        ?>
    </div>

    <div class="test-box <?php echo (strpos($decoded_hook, 'page_setia-settings') !== false) ? 'success' : 'error'; ?>">
        <h2>🎯 Final Result</h2>
        <?php
        $is_settings_page = (
            $decoded_hook == 'setia-content-generator_page_setia-settings' ||
            $decoded_hook == 'toplevel_page_setia-settings' ||
            strpos($decoded_hook, 'setia-settings') !== false ||
            strpos($decoded_hook, 'تولید-محتوا-با-هوش-مصنوعی_page_setia-settings') !== false ||
            strpos($decoded_hook, 'page_setia-settings') !== false
        );
        
        if ($is_settings_page) {
            echo "<h3 style='color: #10b981;'>✅ SUCCESS!</h3>";
            echo "<p>Settings page would be detected correctly with the new conditions.</p>";
            echo "<p><strong>CSS and JS files would be loaded properly.</strong></p>";
        } else {
            echo "<h3 style='color: #ef4444;'>❌ FAILED!</h3>";
            echo "<p>Settings page would still not be detected.</p>";
        }
        ?>
    </div>

    <div class="test-box info">
        <h2>🔗 Next Steps</h2>
        <p>اگر تست موفق بود، حالا می‌توانید صفحه تنظیمات واقعی را امتحان کنید:</p>
        <p><a href="<?php echo admin_url('admin.php?page=setia-settings&setia_debug=1&test_fix=1&nocache=' . time()); ?>" target="_blank" style="background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🚀 تست صفحه تنظیمات SETIA</a></p>
        
        <p style="margin-top: 20px;">ابزارهای اضافی:</p>
        <p><a href="debug-settings.php" target="_blank">🔍 گزارش Debug</a> | <a href="clear-cache.php" target="_blank">🗑️ پاک کردن کش</a></p>
    </div>

    <div class="test-box">
        <h2>📝 Technical Details</h2>
        <p><strong>مشکل:</strong> نام پلاگین فارسی در URL به صورت encoded ذخیره می‌شود</p>
        <p><strong>راه‌حل:</strong> استفاده از urldecode() و شرایط انعطاف‌پذیرتر</p>
        <p><strong>زمان تست:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        <p><strong>Asset Version:</strong> <?php echo get_option('setia_asset_version'); ?></p>
    </div>
</body>
</html>
