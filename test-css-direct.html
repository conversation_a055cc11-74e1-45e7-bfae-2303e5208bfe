<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست مستقیم CSS - SETIA</title>
    
    <!-- بارگذاری مستقیم CSS -->
    <link rel="stylesheet" href="assets/css/test-simple.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="assets/css/admin.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="assets/css/admin-settings.css?v=<?php echo time(); ?>">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f1;
            direction: rtl;
            text-align: right;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #dc3545;
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 14px;
            z-index: 10000;
            transition: all 0.3s ease;
        }
        
        .status-indicator.loaded {
            background: #28a745;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #0073aa;
            padding-bottom: 10px;
        }
        
        .css-test-box {
            margin: 15px 0;
            padding: 15px;
            border: 2px dashed #ccc;
            border-radius: 6px;
            background: #fff;
        }
        
        .css-test-box.loaded {
            border-color: #28a745;
            background: #d4edda;
        }
        
        button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        button:hover {
            background: #005a87;
        }
        
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="status-indicator" id="statusIndicator">
        در حال بارگذاری CSS...
    </div>
    
    <div class="main-container">
        <h1>🎨 تست مستقیم CSS - پلاگین SETIA</h1>
        
        <div class="test-section">
            <h2>📊 وضعیت بارگذاری CSS</h2>
            <div class="css-test-box" id="testSimpleCSS">
                <strong>test-simple.css:</strong> <span id="testSimpleStatus">در حال بررسی...</span>
            </div>
            <div class="css-test-box" id="adminCSS">
                <strong>admin.css:</strong> <span id="adminStatus">در حال بررسی...</span>
            </div>
            <div class="css-test-box" id="adminSettingsCSS">
                <strong>admin-settings.css:</strong> <span id="adminSettingsStatus">در حال بررسی...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 تست‌های عملکردی</h2>
            <button onclick="checkCSSLoading()">بررسی بارگذاری CSS</button>
            <button onclick="testCSSRules()">تست قوانین CSS</button>
            <button onclick="testResponsive()">تست Responsive</button>
            <button onclick="downloadCSSFiles()">دانلود فایل‌های CSS</button>
            
            <div class="results" id="testResults"></div>
        </div>
        
        <div class="test-section setia-test-container setia-test-animation">
            <div class="setia-test-title">تست استایل‌های SETIA</div>
            <div class="setia-test-content setia-persian-text">
                این بخش برای تست استایل‌های پلاگین SETIA طراحی شده است.
                اگر این متن با استایل مناسب نمایش داده می‌شود، یعنی CSS به درستی بارگذاری شده است.
            </div>
            
            <div class="setia-gradient-bg" style="margin-top: 15px;">
                تست Gradient Background
            </div>
            
            <div class="setia-shadow-box" style="margin-top: 15px; padding: 15px; background: white;">
                تست Shadow Effect
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 ابزارهای تشخیص</h2>
            <div>
                <strong>User Agent:</strong> <span id="userAgent"></span>
            </div>
            <div>
                <strong>Screen Resolution:</strong> <span id="screenRes"></span>
            </div>
            <div>
                <strong>Viewport Size:</strong> <span id="viewportSize"></span>
            </div>
            <div>
                <strong>CSS Support:</strong> <span id="cssSupport"></span>
            </div>
        </div>
    </div>

    <script>
        // تست خودکار هنگام بارگذاری
        window.addEventListener('load', function() {
            console.log('🚀 صفحه تست CSS بارگذاری شد');
            
            // به‌روزرسانی اطلاعات سیستم
            updateSystemInfo();
            
            // تست خودکار CSS
            setTimeout(checkCSSLoading, 500);
            
            // تست نشانگر بارگذاری
            setTimeout(function() {
                const indicator = document.getElementById('statusIndicator');
                indicator.textContent = 'CSS بارگذاری شد ✅';
                indicator.classList.add('loaded');
            }, 1000);
        });
        
        function updateSystemInfo() {
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('screenRes').textContent = screen.width + 'x' + screen.height;
            document.getElementById('viewportSize').textContent = window.innerWidth + 'x' + window.innerHeight;
            
            // تست پشتیبانی CSS
            const cssSupport = [];
            if (CSS.supports('display', 'grid')) cssSupport.push('Grid');
            if (CSS.supports('display', 'flex')) cssSupport.push('Flexbox');
            if (CSS.supports('backdrop-filter', 'blur(10px)')) cssSupport.push('Backdrop Filter');
            document.getElementById('cssSupport').textContent = cssSupport.join(', ') || 'محدود';
        }
        
        function checkCSSLoading() {
            const results = document.getElementById('testResults');
            results.innerHTML = 'در حال بررسی بارگذاری CSS...\n\n';
            
            // بررسی لینک‌های CSS
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            results.innerHTML += `تعداد کل لینک‌های CSS: ${cssLinks.length}\n`;
            
            cssLinks.forEach((link, index) => {
                const href = link.href;
                const isSetia = href.includes('setia') || href.includes('admin') || href.includes('test-simple');
                results.innerHTML += `${index + 1}. ${href} ${isSetia ? '(SETIA)' : ''}\n`;
            });
            
            // تست دسترسی به فایل‌ها
            results.innerHTML += '\n--- تست دسترسی ---\n';
            testFileAccess('assets/css/test-simple.css', 'testSimpleCSS', 'testSimpleStatus');
            testFileAccess('assets/css/admin.css', 'adminCSS', 'adminStatus');
            testFileAccess('assets/css/admin-settings.css', 'adminSettingsCSS', 'adminSettingsStatus');
        }
        
        async function testFileAccess(file, boxId, statusId) {
            try {
                const response = await fetch(file);
                const box = document.getElementById(boxId);
                const status = document.getElementById(statusId);
                
                if (response.ok) {
                    const size = response.headers.get('content-length') || 'نامشخص';
                    status.textContent = `✅ موفق (${response.status}) - حجم: ${size} بایت`;
                    status.className = 'success';
                    box.classList.add('loaded');
                } else {
                    status.textContent = `❌ خطا (${response.status})`;
                    status.className = 'error';
                }
            } catch (error) {
                const status = document.getElementById(statusId);
                status.textContent = `❌ خطای شبکه: ${error.message}`;
                status.className = 'error';
            }
        }
        
        function testCSSRules() {
            const results = document.getElementById('testResults');
            results.innerHTML = 'تست قوانین CSS...\n\n';
            
            // تست computed styles
            const testElement = document.querySelector('.setia-test-container');
            if (testElement) {
                const styles = window.getComputedStyle(testElement);
                results.innerHTML += `Background Color: ${styles.backgroundColor}\n`;
                results.innerHTML += `Border: ${styles.border}\n`;
                results.innerHTML += `Padding: ${styles.padding}\n`;
                results.innerHTML += `Direction: ${styles.direction}\n`;
            }
            
            // تست CSS variables
            const rootStyles = window.getComputedStyle(document.documentElement);
            const primaryColor = rootStyles.getPropertyValue('--primary-color');
            results.innerHTML += `\nCSS Variable --primary-color: ${primaryColor || 'تعریف نشده'}\n`;
        }
        
        function testResponsive() {
            const results = document.getElementById('testResults');
            results.innerHTML = 'تست Responsive Design...\n\n';
            
            const breakpoints = [
                { name: 'Mobile', width: 375 },
                { name: 'Tablet', width: 768 },
                { name: 'Desktop', width: 1200 }
            ];
            
            breakpoints.forEach(bp => {
                // شبیه‌سازی تغییر اندازه
                const mediaQuery = window.matchMedia(`(max-width: ${bp.width}px)`);
                results.innerHTML += `${bp.name} (${bp.width}px): ${mediaQuery.matches ? 'فعال' : 'غیرفعال'}\n`;
            });
        }
        
        async function downloadCSSFiles() {
            const results = document.getElementById('testResults');
            results.innerHTML = 'دانلود و بررسی محتوای فایل‌های CSS...\n\n';
            
            const files = [
                'assets/css/test-simple.css',
                'assets/css/admin.css',
                'assets/css/admin-settings.css'
            ];
            
            for (const file of files) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        const content = await response.text();
                        const lines = content.split('\n').length;
                        const size = content.length;
                        results.innerHTML += `✅ ${file}: ${lines} خط، ${size} کاراکتر\n`;
                        
                        // بررسی محتوای کلیدی
                        if (content.includes('SETIA')) {
                            results.innerHTML += `   - شامل کلمه SETIA ✅\n`;
                        }
                        if (content.includes('@font-face')) {
                            results.innerHTML += `   - شامل تعریف فونت ✅\n`;
                        }
                        if (content.includes(':root')) {
                            results.innerHTML += `   - شامل CSS Variables ✅\n`;
                        }
                    } else {
                        results.innerHTML += `❌ ${file}: خطا ${response.status}\n`;
                    }
                } catch (error) {
                    results.innerHTML += `❌ ${file}: ${error.message}\n`;
                }
            }
        }
    </script>
</body>
</html>
