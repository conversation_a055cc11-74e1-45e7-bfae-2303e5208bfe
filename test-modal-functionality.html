<!DOCTYPE html>
<html dir="rtl" lang="fa">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست عملکرد مودال زمانبندی</title>
    <style>
        body {
            font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f1f1f1;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .success { 
            color: #28a745; 
            background: #d4edda; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 5px 0; 
            border-right: 4px solid #28a745; 
        }
        .error { 
            color: #dc3545; 
            background: #f8d7da; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 5px 0; 
            border-right: 4px solid #dc3545; 
        }
        .warning { 
            color: #856404; 
            background: #fff3cd; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 5px 0; 
            border-right: 4px solid #ffc107; 
        }
        .info { 
            color: #0c5460; 
            background: #d1ecf1; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 5px 0; 
            border-right: 4px solid #17a2b8; 
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        .test-button:hover {
            background: #005a87;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        #results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-right: 3px solid #007cba;
            background: white;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #007cba;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .manual-test {
            background: #e7f3ff;
            border: 1px solid #007cba;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border-right: 3px solid #28a745;
        }
        .checklist input[type="checkbox"] {
            margin-left: 10px;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 تست عملکرد مودال زمانبندی SETIA</h1>
        
        <div class="test-section">
            <h2>📋 دستورالعمل تست دستی</h2>
            <div class="manual-test">
                <p><strong>مراحل تست:</strong></p>
                <ol>
                    <li>ابتدا روی دکمه "باز کردن صفحه زمانبندی" کلیک کنید</li>
                    <li>در صفحه باز شده، روی دکمه "افزودن زمانبندی جدید" کلیک کنید</li>
                    <li>فرم را پر کنید و روی "ایجاد برنامه" کلیک کنید</li>
                    <li>نتایج را در چک‌لیست زیر علامت‌گذاری کنید</li>
                </ol>
            </div>
            
            <button class="test-button" onclick="openSchedulerPage()">
                🔗 باز کردن صفحه زمانبندی
            </button>
            
            <button class="test-button" onclick="openSchedulerInFrame()">
                📱 نمایش در فریم (برای تست راحت‌تر)
            </button>
        </div>

        <div class="test-section">
            <h2>✅ چک‌لیست تست مودال</h2>
            <ul class="checklist">
                <li>
                    <input type="checkbox" id="modal-opens"> 
                    <label for="modal-opens">مودال "افزودن زمانبندی جدید" باز می‌شود</label>
                </li>
                <li>
                    <input type="checkbox" id="form-fields"> 
                    <label for="form-fields">تمام فیلدهای فرم قابل مشاهده و قابل ویرایش هستند</label>
                </li>
                <li>
                    <input type="checkbox" id="form-validation"> 
                    <label for="form-validation">اعتبارسنجی فرم کار می‌کند (فیلدهای خالی خطا می‌دهند)</label>
                </li>
                <li>
                    <input type="checkbox" id="form-submits"> 
                    <label for="form-submits">فرم با موفقیت ارسال می‌شود</label>
                </li>
                <li>
                    <input type="checkbox" id="modal-closes"> 
                    <label for="modal-closes">مودال پس از ارسال موفق فرم بسته می‌شود</label>
                </li>
                <li>
                    <input type="checkbox" id="success-message"> 
                    <label for="success-message">پیام موفقیت نمایش داده می‌شود</label>
                </li>
                <li>
                    <input type="checkbox" id="list-updates"> 
                    <label for="list-updates">لیست زمانبندی‌ها به‌روزرسانی می‌شود</label>
                </li>
                <li>
                    <input type="checkbox" id="form-resets"> 
                    <label for="form-resets">فرم پس از ارسال موفق پاک می‌شود</label>
                </li>
            </ul>
            
            <button class="test-button" onclick="generateReport()" style="background: #28a745;">
                📊 تولید گزارش تست
            </button>
        </div>

        <div class="test-section">
            <h2>🔧 تست‌های خودکار AJAX</h2>
            <button class="test-button" onclick="testAjaxHandlers()">
                🚀 تست اتصال AJAX
            </button>
            
            <button class="test-button" onclick="testGetSchedules()">
                📋 تست دریافت زمانبندی‌ها
            </button>
            
            <button class="test-button" onclick="testSaveSchedule()">
                💾 تست ذخیره زمانبندی
            </button>
        </div>

        <div id="results">
            <div class="info">📝 نتایج تست‌ها اینجا نمایش داده می‌شوند...</div>
        </div>

        <div class="iframe-container" id="iframe-container" style="display: none;">
            <iframe id="scheduler-frame" src=""></iframe>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        const ajaxUrl = '/WP/wp-admin/admin-ajax.php';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `${new Date().toLocaleTimeString('fa-IR')} - ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function openSchedulerPage() {
            window.open('/WP/wp-admin/admin.php?page=setia-scheduler', '_blank');
            addResult('🔗 صفحه زمانبندی در تب جدید باز شد', 'info');
        }
        
        function openSchedulerInFrame() {
            const container = document.getElementById('iframe-container');
            const frame = document.getElementById('scheduler-frame');
            frame.src = '/WP/wp-admin/admin.php?page=setia-scheduler';
            container.style.display = 'block';
            addResult('📱 صفحه زمانبندی در فریم بارگذاری شد', 'info');
        }
        
        function generateReport() {
            const checkboxes = document.querySelectorAll('.checklist input[type="checkbox"]');
            let passed = 0;
            let total = checkboxes.length;
            let issues = [];
            
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    passed++;
                } else {
                    const label = checkbox.nextElementSibling.textContent;
                    issues.push(label);
                }
            });
            
            addResult(`📊 گزارش تست: ${passed}/${total} مورد موفق`, passed === total ? 'success' : 'warning');
            
            if (issues.length > 0) {
                addResult('❌ مشکلات شناسایی شده:', 'error');
                issues.forEach(issue => {
                    addResult(`• ${issue}`, 'error');
                });
            } else {
                addResult('🎉 تمام تست‌ها موفق! مودال کاملاً کار می‌کند.', 'success');
            }
        }
        
        async function getNonce() {
            return new Promise((resolve, reject) => {
                $.get('/WP/wp-admin/admin.php?page=setia-scheduler', function(data) {
                    const match = data.match(/var nonce = '([^']+)'/);
                    if (match) {
                        resolve(match[1]);
                    } else {
                        reject('Nonce not found');
                    }
                }).fail(reject);
            });
        }
        
        async function testAjaxHandlers() {
            addResult('🔄 در حال تست اتصال AJAX...', 'info');
            
            try {
                const nonce = await getNonce();
                addResult('✅ Nonce دریافت شد', 'success');
                
                // تست وجود اکشن‌های AJAX
                const actions = [
                    'setia_save_schedule',
                    'setia_get_schedules', 
                    'setia_delete_schedule'
                ];
                
                addResult(`🧪 تست ${actions.length} اکشن AJAX...`, 'info');
                
            } catch (error) {
                addResult('❌ خطا در دریافت nonce: ' + error, 'error');
            }
        }
        
        async function testGetSchedules() {
            addResult('🔄 تست دریافت زمانبندی‌ها...', 'info');
            
            try {
                const nonce = await getNonce();
                
                $.ajax({
                    url: ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'setia_get_schedules',
                        nonce: nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            const count = response.data.schedules ? response.data.schedules.length : 0;
                            addResult(`✅ دریافت زمانبندی‌ها موفق: ${count} مورد یافت شد`, 'success');
                        } else {
                            addResult('❌ خطا در دریافت: ' + (response.data ? response.data.message : 'نامشخص'), 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        addResult('❌ خطا در AJAX: ' + error, 'error');
                    }
                });
                
            } catch (error) {
                addResult('❌ خطا: ' + error, 'error');
            }
        }
        
        async function testSaveSchedule() {
            addResult('🔄 تست ذخیره زمانبندی...', 'info');
            
            try {
                const nonce = await getNonce();
                
                $.ajax({
                    url: ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'setia_save_schedule',
                        nonce: nonce,
                        title: 'تست خودکار',
                        topic: 'موضوع تست خودکار',
                        keywords: 'تست, خودکار',
                        frequency: 'daily',
                        status: 'active',
                        schedule_id: 0
                    },
                    success: function(response) {
                        if (response.success) {
                            addResult('✅ ذخیره زمانبندی موفق', 'success');
                        } else {
                            addResult('❌ خطا در ذخیره: ' + (response.data ? response.data.message : 'نامشخص'), 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        addResult('❌ خطا در AJAX: ' + error, 'error');
                    }
                });
                
            } catch (error) {
                addResult('❌ خطا: ' + error, 'error');
            }
        }
        
        // تست اولیه
        $(document).ready(function() {
            addResult('🚀 سیستم تست مودال آماده است', 'success');
            addResult('📝 لطفاً ابتدا صفحه زمانبندی را باز کنید و تست‌های دستی را انجام دهید', 'info');
        });
    </script>
</body>
</html>
