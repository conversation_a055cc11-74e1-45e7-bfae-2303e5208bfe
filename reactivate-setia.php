<?php
/**
 * SETIA Plugin Reactivation Script
 * این اسکریپت پلاگین SETIA را مجدداً فعال می‌کند و تمام تنظیمات را بازنشانی می‌کند
 */

// Security check
if (!defined('ABSPATH')) {
    // Try to load WordPress
    $wp_load_paths = [
        '../../../wp-load.php',
        '../../../../wp-load.php', 
        '../../../../../wp-load.php',
        dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
        dirname(dirname(dirname(dirname(__DIR__)))) . '/wp-load.php'
    ];
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this script from WordPress admin or ensure wp-load.php is accessible.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('شما دسترسی کافی برای اجرای این اسکریپت ندارید.');
}

echo "<h1>🔄 SETIA Plugin Reactivation</h1>";
echo "<div style='font-family: Tahoma, Arial, sans-serif; direction: rtl; text-align: right;'>";

// Step 1: Load plugin file
echo "<h2>1️⃣ بارگذاری فایل اصلی پلاگین</h2>";
$plugin_file = plugin_dir_path(__FILE__) . 'setia-content-generator.php';
if (file_exists($plugin_file)) {
    require_once $plugin_file;
    echo "<p style='color: green;'>✅ فایل اصلی پلاگین بارگذاری شد</p>";
} else {
    echo "<p style='color: red;'>❌ فایل اصلی پلاگین یافت نشد</p>";
    die();
}

// Step 2: Create database tables
echo "<h2>2️⃣ ایجاد جداول دیتابیس</h2>";
create_setia_database_tables();

// Step 3: Set default options
echo "<h2>3️⃣ تنظیم گزینه‌های پیش‌فرض</h2>";
$default_settings = [
    'gemini_api_key' => '',
    'gemma_api_key' => '',
    'imagine_art_api_key' => '',
    'default_tone' => 'عادی',
    'default_length' => 'متوسط',
    'enable_seo' => 'yes',
    'enable_image_generation' => 'yes',
    'default_image_style' => 'realistic',
    'default_aspect_ratio' => '16:9',
    'internal_cron_interval' => 15
];

update_option('setia_settings', $default_settings);
echo "<p style='color: green;'>✅ تنظیمات پیش‌فرض ذخیره شدند</p>";

// Step 4: Register custom cron schedules
echo "<h2>4️⃣ ثبت زمانبندی‌های کرون سفارشی</h2>";
add_filter('cron_schedules', 'setia_add_custom_cron_intervals');

function setia_add_custom_cron_intervals($schedules) {
    $schedules['setia_15min'] = array(
        'interval' => 15 * 60,
        'display' => 'هر 15 دقیقه'
    );
    $schedules['setia_30min'] = array(
        'interval' => 30 * 60,
        'display' => 'هر 30 دقیقه'
    );
    $schedules['setia_2hours'] = array(
        'interval' => 2 * 60 * 60,
        'display' => 'هر 2 ساعت'
    );
    $schedules['setia_6hours'] = array(
        'interval' => 6 * 60 * 60,
        'display' => 'هر 6 ساعت'
    );
    return $schedules;
}

echo "<p style='color: green;'>✅ زمانبندی‌های کرون سفارشی ثبت شدند</p>";

// Step 5: Clear all caches
echo "<h2>5️⃣ پاک کردن کش‌ها</h2>";
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "<p style='color: green;'>✅ کش WordPress پاک شد</p>";
}

// Update asset versions
$current_time = time();
update_option('setia_asset_version', $current_time);
update_option('setia_css_version', $current_time);
update_option('setia_js_version', $current_time);
echo "<p style='color: green;'>✅ نسخه‌های asset بروزرسانی شدند</p>";

// Step 6: Test scheduler class
echo "<h2>6️⃣ تست کلاس زمانبندی</h2>";
$scheduler_file = plugin_dir_path(__FILE__) . 'includes/scheduler.php';
if (file_exists($scheduler_file)) {
    require_once $scheduler_file;
    if (class_exists('SETIA_Scheduler')) {
        echo "<p style='color: green;'>✅ کلاس SETIA_Scheduler بارگذاری شد</p>";
        
        // Test scheduler initialization
        try {
            $scheduler = new SETIA_Scheduler(null);
            echo "<p style='color: green;'>✅ نمونه‌ای از کلاس زمانبندی ایجاد شد</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطا در ایجاد نمونه زمانبندی: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ کلاس SETIA_Scheduler یافت نشد</p>";
    }
} else {
    echo "<p style='color: red;'>❌ فایل scheduler.php موجود نیست</p>";
}

echo "</div>";

// Function to create database tables
function create_setia_database_tables() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    
    // Table 1: Generated content
    $table1 = $wpdb->prefix . 'setia_generated_content';
    $sql1 = "CREATE TABLE $table1 (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        post_id mediumint(9) DEFAULT NULL,
        topic varchar(255) NOT NULL,
        keywords text NOT NULL,
        tone varchar(50) NOT NULL,
        category varchar(100) NOT NULL,
        length varchar(50) NOT NULL,
        generated_text longtext NOT NULL,
        generated_image_url varchar(255) DEFAULT NULL,
        seo_meta text DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) $charset_collate;";
    
    // Table 2: Content schedules
    $table2 = $wpdb->prefix . 'setia_content_schedules';
    $sql2 = "CREATE TABLE $table2 (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        title varchar(255) NOT NULL,
        topic varchar(255) NOT NULL,
        keywords text,
        category_id mediumint(9) DEFAULT NULL,
        tone varchar(50) DEFAULT 'عادی',
        length varchar(50) DEFAULT 'متوسط',
        frequency varchar(50) NOT NULL,
        status tinyint(1) DEFAULT 1,
        daily_limit int(11) DEFAULT 1,
        generate_image tinyint(1) DEFAULT 1,
        last_run datetime DEFAULT NULL,
        next_run datetime DEFAULT NULL,
        generated_count int(11) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) $charset_collate;";
    
    // Table 3: Scheduler logs
    $table3 = $wpdb->prefix . 'setia_scheduler_logs';
    $sql3 = "CREATE TABLE $table3 (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        schedule_id mediumint(9) DEFAULT NULL,
        message text NOT NULL,
        type varchar(20) DEFAULT 'info',
        post_id mediumint(9) DEFAULT NULL,
        execution_time varchar(50) DEFAULT NULL,
        memory_usage varchar(50) DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY schedule_id (schedule_id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    
    $result1 = dbDelta($sql1);
    $result2 = dbDelta($sql2);
    $result3 = dbDelta($sql3);
    
    echo "<p style='color: green;'>✅ جداول دیتابیس ایجاد شدند</p>";
    
    // Verify tables were created
    $tables_created = 0;
    if ($wpdb->get_var("SHOW TABLES LIKE '$table1'")) $tables_created++;
    if ($wpdb->get_var("SHOW TABLES LIKE '$table2'")) $tables_created++;
    if ($wpdb->get_var("SHOW TABLES LIKE '$table3'")) $tables_created++;
    
    echo "<p style='color: green;'>✅ $tables_created از 3 جدول با موفقیت ایجاد شدند</p>";
}

echo "<h2>🎉 فعال‌سازی مجدد پلاگین تکمیل شد!</h2>";
echo "<p><strong>مراحل بعدی:</strong></p>";
echo "<ul>";
echo "<li>🔗 <a href='" . admin_url('admin.php?page=setia-scheduler') . "'>بازگشت به صفحه زمانبندی</a></li>";
echo "<li>🔗 <a href='" . admin_url('admin.php?page=setia-settings') . "'>تنظیم کلیدهای API</a></li>";
echo "<li>🔗 <a href='" . admin_url('plugins.php') . "'>مدیریت پلاگین‌ها</a></li>";
echo "</ul>";
?>
