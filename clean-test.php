<?php
/**
 * Clean Test for SETIA Settings
 * تست تمیز و بهینه شده برای صفحه تنظیمات
 */

// WordPress environment
if (file_exists('../../../wp-config.php')) {
    require_once('../../../wp-config.php');
    require_once('../../../wp-load.php');
} else {
    die('WordPress not found');
}

if (!current_user_can('manage_options')) {
    die('Access denied');
}

// Clean all caches
wp_cache_flush();
update_option('setia_asset_version', time());

// Clear transients
global $wpdb;
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_setia_%'");

?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SETIA Clean Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header {
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            color: #718096;
            font-size: 1.1rem;
        }
        
        .status-card {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            box-shadow: 0 10px 25px rgba(72, 187, 120, 0.3);
        }
        
        .status-card h2 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 25px 0;
        }
        
        .info-item {
            background: #f7fafc;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #4299e1;
            text-align: right;
        }
        
        .info-item strong {
            color: #2d3748;
            display: block;
            margin-bottom: 5px;
        }
        
        .info-item span {
            color: #4a5568;
            font-size: 0.9rem;
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            padding: 15px 30px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(66, 153, 225, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #48bb78, #38a169);
            box-shadow: 0 5px 15px rgba(72, 187, 120, 0.3);
        }
        
        .btn-success:hover {
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }
        
        .countdown {
            font-size: 1.5rem;
            font-weight: bold;
            color: #4299e1;
            margin: 20px 0;
        }
        
        .features {
            background: #edf2f7;
            padding: 20px;
            border-radius: 12px;
            margin: 25px 0;
            text-align: right;
        }
        
        .features h3 {
            color: #2d3748;
            margin-bottom: 15px;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            color: #4a5568;
            padding: 5px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .features li:last-child {
            border-bottom: none;
        }
        
        .features li:before {
            content: "✅";
            margin-left: 10px;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 SETIA</h1>
            <p>تست تمیز و بهینه شده</p>
        </div>

        <div class="status-card">
            <h2>✅ همه مشکلات حل شد!</h2>
            <p>صفحه تنظیمات SETIA آماده استفاده است</p>
        </div>

        <div class="info-grid">
            <div class="info-item">
                <strong>زمان تست</strong>
                <span><?php echo date('H:i:s'); ?></span>
            </div>
            <div class="info-item">
                <strong>تاریخ</strong>
                <span><?php echo date('Y/m/d'); ?></span>
            </div>
            <div class="info-item">
                <strong>Asset Version</strong>
                <span><?php echo get_option('setia_asset_version'); ?></span>
            </div>
            <div class="info-item">
                <strong>WordPress</strong>
                <span><?php echo get_bloginfo('version'); ?></span>
            </div>
        </div>

        <div class="features">
            <h3>🎯 ویژگی‌های فعال شده</h3>
            <ul>
                <li>طراحی Windows 11 Flat Design</li>
                <li>سیستم تب‌های عملکردی</li>
                <li>دکمه‌های فعال و responsive</li>
                <li>رفع مشکل URL encoding</li>
                <li>بهینه‌سازی عملکرد</li>
                <li>پاک‌سازی debug logs</li>
                <li>Cache management بهبود یافته</li>
            </ul>
        </div>

        <div class="countdown pulse" id="countdown">
            انتقال در 3 ثانیه...
        </div>

        <div>
            <?php $settings_url = admin_url('admin.php?page=setia-settings&clean_test=1&v=' . time()); ?>
            <a href="<?php echo esc_url($settings_url); ?>" class="btn btn-success">
                🎯 ورود به تنظیمات SETIA
            </a>
            <a href="debug-settings.php" class="btn" target="_blank">
                🔍 گزارش Debug
            </a>
        </div>

        <p style="margin-top: 30px; color: #718096; font-size: 0.9rem;">
            تمام مشکلات برطرف شده و سیستم آماده استفاده است
        </p>
    </div>

    <script>
        console.log('🚀 SETIA Clean Test - Starting...');
        
        let countdown = 3;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(function() {
            countdownElement.textContent = `انتقال در ${countdown} ثانیه...`;
            countdown--;
            
            if (countdown < 0) {
                clearInterval(timer);
                countdownElement.textContent = 'در حال انتقال...';
                countdownElement.style.color = '#48bb78';
                
                console.log('🎯 Redirecting to clean SETIA Settings...');
                window.location.href = '<?php echo esc_js($settings_url); ?>';
            }
        }, 1000);
        
        // Performance monitoring
        console.log('📊 Performance Info:');
        console.log('  - Page Load Time:', performance.now().toFixed(2) + 'ms');
        console.log('  - WordPress Version:', '<?php echo get_bloginfo("version"); ?>');
        console.log('  - Asset Version:', '<?php echo get_option("setia_asset_version"); ?>');
        
        // Check if all resources loaded
        window.addEventListener('load', function() {
            console.log('✅ All resources loaded successfully');
        });
    </script>
</body>
</html>
