<?php
/**
 * Quick Fix for SETIA Scheduler Issues
 * اسکریپت سریع برای رفع مشکلات فوری زمانبندی SETIA
 */

// Load WordPress
$wp_load_paths = [
    '../../../wp-load.php',
    '../../../../wp-load.php',
    '../../../../../wp-load.php',
    dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
    dirname(dirname(dirname(dirname(__DIR__)))) . '/wp-load.php'
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('WordPress not found. Please run this script from WordPress admin or ensure wp-load.php is accessible.');
}

// Check admin access
if (!current_user_can('manage_options')) {
    die('دسترسی غیرمجاز');
}

echo "<h1>🚀 Quick Fix SETIA Issues</h1>";
echo "<div style='font-family: Tahoma; direction: rtl; text-align: right;'>";

// 1. Force create database tables
echo "<h2>1️⃣ ایجاد اجباری جداول دیتابیس</h2>";
global $wpdb;
$charset_collate = $wpdb->get_charset_collate();

// Create tables with proper structure
$tables = [
    'setia_generated_content' => "CREATE TABLE {$wpdb->prefix}setia_generated_content (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        post_id mediumint(9) DEFAULT NULL,
        topic varchar(255) NOT NULL,
        keywords text NOT NULL,
        tone varchar(50) NOT NULL,
        category varchar(100) NOT NULL,
        length varchar(50) NOT NULL,
        generated_text longtext NOT NULL,
        generated_image_url varchar(255) DEFAULT NULL,
        seo_meta text DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) $charset_collate;",
    
    'setia_content_schedules' => "CREATE TABLE {$wpdb->prefix}setia_content_schedules (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        title varchar(255) NOT NULL,
        topic varchar(255) NOT NULL,
        keywords text,
        category_id mediumint(9) DEFAULT NULL,
        tone varchar(50) DEFAULT 'عادی',
        length varchar(50) DEFAULT 'متوسط',
        frequency varchar(50) NOT NULL,
        status tinyint(1) DEFAULT 1,
        daily_limit int(11) DEFAULT 1,
        generate_image tinyint(1) DEFAULT 1,
        last_run datetime DEFAULT NULL,
        next_run datetime DEFAULT NULL,
        generated_count int(11) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) $charset_collate;",
    
    'setia_scheduler_logs' => "CREATE TABLE {$wpdb->prefix}setia_scheduler_logs (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        schedule_id mediumint(9) DEFAULT NULL,
        message text NOT NULL,
        type varchar(20) DEFAULT 'info',
        post_id mediumint(9) DEFAULT NULL,
        execution_time varchar(50) DEFAULT NULL,
        memory_usage varchar(50) DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY schedule_id (schedule_id)
    ) $charset_collate;"
];

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

foreach ($tables as $table_name => $sql) {
    $result = dbDelta($sql);
    $full_table_name = $wpdb->prefix . $table_name;
    
    if ($wpdb->get_var("SHOW TABLES LIKE '$full_table_name'")) {
        echo "<p style='color: green;'>✅ جدول $table_name ایجاد شد</p>";
    } else {
        echo "<p style='color: red;'>❌ خطا در ایجاد جدول $table_name</p>";
    }
}

// 2. Enable WordPress Cron if disabled
echo "<h2>2️⃣ بررسی و فعال‌سازی WordPress Cron</h2>";
if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
    echo "<p style='color: orange;'>⚠️ WordPress Cron غیرفعال است در wp-config.php</p>";
    echo "<p>برای فعال‌سازی، خط زیر را از wp-config.php حذف کنید:</p>";
    echo "<code>define('DISABLE_WP_CRON', true);</code>";
} else {
    echo "<p style='color: green;'>✅ WordPress Cron فعال است</p>";
}

// 3. Register AJAX actions manually
echo "<h2>3️⃣ ثبت دستی AJAX Actions</h2>";

// Load scheduler class
$scheduler_file = __DIR__ . '/includes/scheduler.php';
if (file_exists($scheduler_file)) {
    require_once $scheduler_file;
    
    if (class_exists('SETIA_Scheduler')) {
        // Create scheduler instance
        $scheduler = new SETIA_Scheduler(null);
        
        // Register AJAX actions
        $ajax_actions = [
            'setia_get_schedules' => 'ajax_get_schedules',
            'setia_save_schedule' => 'ajax_save_schedule',
            'setia_delete_schedule' => 'ajax_delete_schedule',
            'setia_toggle_schedule' => 'ajax_toggle_schedule',
            'setia_run_schedule_now' => 'ajax_run_schedule_now',
            'setia_get_schedule_logs' => 'ajax_get_schedule_logs',
            'setia_clear_schedule_logs' => 'ajax_clear_schedule_logs'
        ];
        
        foreach ($ajax_actions as $action => $method) {
            add_action("wp_ajax_$action", array($scheduler, $method));
            echo "<p style='color: green;'>✅ AJAX action $action ثبت شد</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ کلاس SETIA_Scheduler یافت نشد</p>";
    }
} else {
    echo "<p style='color: red;'>❌ فایل scheduler.php موجود نیست</p>";
}

// 4. Set default settings
echo "<h2>4️⃣ تنظیم گزینه‌های پیش‌فرض</h2>";
$default_settings = [
    'gemini_api_key' => '',
    'gemma_api_key' => '',
    'imagine_art_api_key' => '',
    'default_tone' => 'عادی',
    'default_length' => 'متوسط',
    'enable_seo' => 'yes',
    'enable_image_generation' => 'yes',
    'default_image_style' => 'realistic',
    'default_aspect_ratio' => '16:9',
    'internal_cron_interval' => 15
];

update_option('setia_settings', $default_settings);
echo "<p style='color: green;'>✅ تنظیمات پیش‌فرض ذخیره شدند</p>";

// 5. Clear cache and update versions
echo "<h2>5️⃣ پاک کردن کش و بروزرسانی</h2>";
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "<p style='color: green;'>✅ کش WordPress پاک شد</p>";
}

$current_time = time();
update_option('setia_asset_version', $current_time);
update_option('setia_css_version', $current_time);
update_option('setia_js_version', $current_time);
echo "<p style='color: green;'>✅ نسخه‌های asset بروزرسانی شدند: $current_time</p>";

// 6. Test current user capabilities
echo "<h2>6️⃣ بررسی دسترسی‌های کاربر فعلی</h2>";
$user = wp_get_current_user();
echo "<p>کاربر فعلی: " . $user->display_name . " (ID: " . $user->ID . ")</p>";

$caps = ['manage_options', 'edit_posts', 'publish_posts', 'upload_files'];
foreach ($caps as $cap) {
    if (current_user_can($cap)) {
        echo "<p style='color: green;'>✅ دسترسی $cap موجود است</p>";
    } else {
        echo "<p style='color: red;'>❌ دسترسی $cap موجود نیست</p>";
    }
}

// 7. Test database connection
echo "<h2>7️⃣ تست اتصال دیتابیس</h2>";
$test_query = $wpdb->get_results("SHOW TABLES LIKE '{$wpdb->prefix}setia_%'");
echo "<p>جداول SETIA موجود: " . count($test_query) . "</p>";

foreach ($test_query as $table) {
    $table_name = array_values((array)$table)[0];
    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    echo "<p style='color: blue;'>📊 $table_name: $count رکورد</p>";
}

echo "</div>";

echo "<h2>🎉 رفع سریع مشکلات تکمیل شد!</h2>";
echo "<p><a href='" . admin_url('admin.php?page=setia-scheduler') . "' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 بازگشت به صفحه زمانبندی</a></p>";
?>
