<?php
/**
 * صفحه تاریخچه پیشرفته تولید محتوا
 * SETIA Content Generator Plugin - Advanced History Page
 *
 * @package SETIA
 * @version 2.0
 */

// جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

// بررسی دسترسی کاربر
if (!current_user_can('edit_posts')) {
    wp_die(__('شما دسترسی لازم برای مشاهده این صفحه را ندارید.'));
}


?>

<!-- Direct CSS link as fallback -->
<link rel="stylesheet" href="<?php echo plugin_dir_url(dirname(__FILE__)) . 'assets/css/history-advanced.css?v=' . time(); ?>" type="text/css" media="all" />

<!-- Debug: Inline styles for immediate testing -->
<style>
.setia-history-advanced {
    direction: rtl !important;
    text-align: right !important;
    font-family: 'Vazir', 'Tahoma', sans-serif !important;
}
.setia-page-header {
    background: linear-gradient(135deg, #2271b1 0%, #135e96 100%) !important;
    color: white !important;
    padding: 2rem !important;
    margin: 0 -20px 2rem -20px !important;
    border-radius: 0 0 8px 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}
.setia-header-content {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
    flex-wrap: wrap !important;
    gap: 1rem !important;
}
.setia-main-title {
    font-size: 2rem !important;
    font-weight: 700 !important;
    margin: 0 0 0.5rem 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}
.setia-stats-dashboard {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 1.5rem !important;
    margin-bottom: 2rem !important;
}
.setia-stat-card {
    background: white !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    padding: 1.5rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    position: relative !important;
    overflow: hidden !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}
.setia-stat-card::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    width: 4px !important;
    height: 100% !important;
    background: #2271b1 !important;
    transition: all 0.3s ease !important;
}
.setia-stat-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
}
.setia-filters-panel {
    background: white !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    margin-bottom: 2rem !important;
    overflow: hidden !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}
.setia-panel-header {
    background: #f6f7f7 !important;
    padding: 1rem 1.5rem !important;
    border-bottom: 1px solid #e0e0e0 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}
.setia-filters-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 1rem !important;
    margin-bottom: 1.5rem !important;
}
.setia-content-panel {
    background: white !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    overflow: hidden !important;
}
.setia-content-table {
    width: 100% !important;
    border-collapse: collapse !important;
    font-size: 0.875rem !important;
}
.setia-content-table th,
.setia-content-table td {
    padding: 1rem !important;
    text-align: right !important;
    border-bottom: 1px solid #e0e0e0 !important;
    vertical-align: middle !important;
}
.setia-content-table th {
    background: #f6f7f7 !important;
    font-weight: 600 !important;
    color: #1d2327 !important;
}
.setia-btn {
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    padding: 0.75rem 1.5rem !important;
    border: none !important;
    border-radius: 4px !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    direction: rtl !important;
}
.setia-btn-primary {
    background: #2271b1 !important;
    color: white !important;
}
.setia-btn-primary:hover {
    background: #135e96 !important;
}
.setia-input,
.setia-select {
    width: 100% !important;
    padding: 0.75rem !important;
    border: 1px solid #c3c4c7 !important;
    border-radius: 4px !important;
    font-size: 0.875rem !important;
    transition: all 0.3s ease !important;
    background: white !important;
    color: #1d2327 !important;
    direction: rtl !important;
    text-align: right !important;
}
</style>



<div class="wrap setia-history-advanced">
    <!-- Header Section -->
    <div class="setia-page-header">
        <div class="setia-header-content">
            <div class="setia-header-title">
                <h1 class="setia-main-title">
                    <span class="setia-icon">📊</span>
                    مدیریت تاریخچه محتوا
                </h1>
                <p class="setia-subtitle">مدیریت کامل و پیشرفته محتوای تولید شده با هوش مصنوعی</p>
            </div>
            <div class="setia-header-actions">
                <button id="refresh-data" class="setia-btn setia-btn-outline">
                    <i class="dashicons dashicons-update"></i>
                    بروزرسانی
                </button>
                <button id="export-excel" class="setia-btn setia-btn-success">
                    <i class="dashicons dashicons-download"></i>
                    خروجی Excel
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Dashboard -->
    <div class="setia-stats-dashboard">
        <div class="setia-stat-card" data-stat="total">
            <div class="setia-stat-icon">📝</div>
            <div class="setia-stat-info">
                <h3 class="setia-stat-title">کل محتوا</h3>
                <span class="setia-stat-number" id="total-content">0</span>
                <span class="setia-stat-change">+0 امروز</span>
            </div>
        </div>
        
        <div class="setia-stat-card" data-stat="published">
            <div class="setia-stat-icon">✅</div>
            <div class="setia-stat-info">
                <h3 class="setia-stat-title">منتشر شده</h3>
                <span class="setia-stat-number" id="published-content">0</span>
                <span class="setia-stat-change">0% نرخ انتشار</span>
            </div>
        </div>
        
        <div class="setia-stat-card" data-stat="drafts">
            <div class="setia-stat-icon">📄</div>
            <div class="setia-stat-info">
                <h3 class="setia-stat-title">پیش‌نویس</h3>
                <span class="setia-stat-number" id="draft-content">0</span>
                <span class="setia-stat-change">آماده انتشار</span>
            </div>
        </div>
        
        <div class="setia-stat-card" data-stat="products">
            <div class="setia-stat-icon">🛒</div>
            <div class="setia-stat-info">
                <h3 class="setia-stat-title">محصولات</h3>
                <span class="setia-stat-number" id="product-content">0</span>
                <span class="setia-stat-change">WooCommerce</span>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="setia-filters-panel">
        <div class="setia-panel-header">
            <h3>فیلترهای پیشرفته</h3>
            <button id="toggle-filters" class="setia-btn setia-btn-ghost">
                <i class="dashicons dashicons-filter"></i>
                نمایش/مخفی کردن
            </button>
        </div>
        
        <div class="setia-filters-content" id="filters-content">
            <div class="setia-filters-grid">
                <div class="setia-filter-group">
                    <label for="date-range-from">از تاریخ (شمسی):</label>
                    <input type="text" id="date-range-from" class="setia-input persian-datepicker" placeholder="1403/01/01">
                </div>
                
                <div class="setia-filter-group">
                    <label for="date-range-to">تا تاریخ (شمسی):</label>
                    <input type="text" id="date-range-to" class="setia-input persian-datepicker" placeholder="1403/12/29">
                </div>
                
                <div class="setia-filter-group">
                    <label for="content-type">نوع محتوا:</label>
                    <select id="content-type" class="setia-select">
                        <option value="">همه انواع</option>
                        <option value="post">مقاله</option>
                        <option value="product">محصول WooCommerce</option>
                        <option value="page">صفحه</option>
                    </select>
                </div>
                
                <div class="setia-filter-group">
                    <label for="status-filter">وضعیت:</label>
                    <select id="status-filter" class="setia-select">
                        <option value="">همه وضعیت‌ها</option>
                        <option value="published">منتشر شده</option>
                        <option value="draft">پیش‌نویس</option>
                        <option value="pending">در انتظار بررسی</option>
                        <option value="failed">ناموفق</option>
                    </select>
                </div>
                
                <div class="setia-filter-group">
                    <label for="category-filter">دسته‌بندی:</label>
                    <select id="category-filter" class="setia-select">
                        <option value="">همه دسته‌ها</option>
                        <!-- Categories will be loaded dynamically -->
                    </select>
                </div>
                
                <div class="setia-filter-group">
                    <label for="keyword-search">جستجوی کلیدواژه:</label>
                    <input type="text" id="keyword-search" class="setia-input" placeholder="عنوان، کلیدواژه یا محتوا...">
                </div>
                
                <div class="setia-filter-group">
                    <label for="word-count-min">حداقل تعداد کلمات:</label>
                    <input type="number" id="word-count-min" class="setia-input" placeholder="0" min="0">
                </div>
                
                <div class="setia-filter-group">
                    <label for="word-count-max">حداکثر تعداد کلمات:</label>
                    <input type="number" id="word-count-max" class="setia-input" placeholder="10000" min="0">
                </div>
            </div>
            
            <div class="setia-filters-actions">
                <button id="apply-filters" class="setia-btn setia-btn-primary">
                    <i class="dashicons dashicons-search"></i>
                    اعمال فیلترها
                </button>
                <button id="reset-filters" class="setia-btn setia-btn-outline">
                    <i class="dashicons dashicons-dismiss"></i>
                    پاک کردن فیلترها
                </button>
            </div>
        </div>
    </div>

    <!-- Content Management Table -->
    <div class="setia-content-panel">
        <div class="setia-panel-header">
            <div class="setia-panel-title">
                <h3>لیست محتوای تولید شده</h3>
                <span class="setia-results-count" id="results-count">0 مورد یافت شد</span>
            </div>
            
            <div class="setia-panel-actions">
                <div class="setia-bulk-actions">
                    <select id="bulk-action" class="setia-select">
                        <option value="">عملیات گروهی</option>
                        <option value="publish">انتشار</option>
                        <option value="draft">تبدیل به پیش‌نویس</option>
                        <option value="delete">حذف</option>
                    </select>
                    <button id="apply-bulk-action" class="setia-btn setia-btn-secondary" disabled>
                        اعمال
                    </button>
                </div>
                
                <div class="setia-view-options">
                    <select id="items-per-page" class="setia-select">
                        <option value="10">10 مورد</option>
                        <option value="25" selected>25 مورد</option>
                        <option value="50">50 مورد</option>
                        <option value="100">100 مورد</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Loading State -->
        <div class="setia-loading-state" id="loading-state" style="display: none;">
            <div class="setia-spinner"></div>
            <p>در حال بارگذاری داده‌ها...</p>
        </div>
        
        <!-- Content Table -->
        <div class="setia-table-container" id="table-container">
            <table class="setia-content-table" id="content-table">
                <thead>
                    <tr>
                        <th class="setia-checkbox-col">
                            <input type="checkbox" id="select-all" class="setia-checkbox">
                        </th>
                        <th class="setia-sortable" data-sort="title">
                            عنوان
                            <i class="dashicons dashicons-sort"></i>
                        </th>
                        <th class="setia-sortable" data-sort="type">
                            نوع
                            <i class="dashicons dashicons-sort"></i>
                        </th>
                        <th class="setia-sortable" data-sort="status">
                            وضعیت
                            <i class="dashicons dashicons-sort"></i>
                        </th>
                        <th class="setia-sortable" data-sort="created_at">
                            تاریخ تولید
                            <i class="dashicons dashicons-sort"></i>
                        </th>
                        <th>کلیدواژه اصلی</th>
                        <th class="setia-sortable" data-sort="word_count">
                            تعداد کلمات
                            <i class="dashicons dashicons-sort"></i>
                        </th>
                        <th class="setia-actions-col">عملیات</th>
                    </tr>
                </thead>
                <tbody id="content-table-body">
                    <!-- Content will be loaded here via AJAX -->
                </tbody>
            </table>
        </div>
        
        <!-- Empty State -->
        <div class="setia-empty-state" id="empty-state" style="display: none;">
            <div class="setia-empty-icon">📭</div>
            <h3>هیچ محتوایی یافت نشد</h3>
            <p>با فیلترهای مختلف جستجو کنید یا محتوای جدید تولید کنید.</p>
            <a href="<?php echo admin_url('admin.php?page=setia-content-generator'); ?>" class="setia-btn setia-btn-primary">
                تولید محتوای جدید
            </a>
        </div>
        
        <!-- Pagination -->
        <div class="setia-pagination" id="pagination-container" style="display: none;">
            <div class="setia-pagination-info">
                <span id="pagination-info">نمایش 0 تا 0 از 0 مورد</span>
            </div>
            <div class="setia-pagination-controls" id="pagination-controls">
                <!-- Pagination buttons will be generated here -->
            </div>
        </div>
    </div>
</div>

<!-- Content Preview Modal -->
<div id="content-preview-modal" class="setia-modal" style="display: none;">
    <div class="setia-modal-overlay"></div>
    <div class="setia-modal-container">
        <div class="setia-modal-header">
            <h3 id="modal-title">پیش‌نمایش محتوا</h3>
            <button class="setia-modal-close" id="close-modal">
                <i class="dashicons dashicons-no-alt"></i>
            </button>
        </div>
        <div class="setia-modal-body" id="modal-content">
            <!-- Content will be loaded here -->
        </div>
        <div class="setia-modal-footer">
            <button id="modal-edit-btn" class="setia-btn setia-btn-primary">ویرایش در وردپرس</button>
            <button id="modal-close-btn" class="setia-btn setia-btn-outline">بستن</button>
        </div>
    </div>
</div>

<!-- Content Edit Modal -->
<div id="content-edit-modal" class="setia-modal" style="display: none;">
    <div class="setia-modal-overlay"></div>
    <div class="setia-modal-container setia-modal-large">
        <div class="setia-modal-header">
            <h3>ویرایش محتوا</h3>
            <button class="setia-modal-close" id="close-edit-modal">
                <i class="dashicons dashicons-no-alt"></i>
            </button>
        </div>
        <div class="setia-modal-body">
            <form id="edit-content-form">
                <div class="setia-form-group">
                    <label for="edit-title">عنوان:</label>
                    <input type="text" id="edit-title" class="setia-input" required>
                </div>

                <div class="setia-form-group">
                    <label for="edit-content">محتوا:</label>
                    <textarea id="edit-content" class="setia-textarea" rows="15" required></textarea>
                </div>

                <div class="setia-form-row">
                    <div class="setia-form-group">
                        <label for="edit-keyword">کلیدواژه اصلی:</label>
                        <input type="text" id="edit-keyword" class="setia-input">
                    </div>

                    <div class="setia-form-group">
                        <label for="edit-category">دسته‌بندی:</label>
                        <select id="edit-category" class="setia-select">
                            <option value="">انتخاب دسته‌بندی</option>
                        </select>
                    </div>
                </div>

                <div class="setia-form-group">
                    <label for="edit-tags">برچسب‌ها (با کاما جدا کنید):</label>
                    <input type="text" id="edit-tags" class="setia-input" placeholder="برچسب1, برچسب2, برچسب3">
                </div>

                <input type="hidden" id="edit-content-id">
            </form>
        </div>
        <div class="setia-modal-footer">
            <button id="save-content-btn" class="setia-btn setia-btn-primary">ذخیره تغییرات</button>
            <button id="cancel-edit-btn" class="setia-btn setia-btn-outline">انصراف</button>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div id="confirmation-modal" class="setia-modal" style="display: none;">
    <div class="setia-modal-overlay"></div>
    <div class="setia-modal-container setia-modal-small">
        <div class="setia-modal-header">
            <h3 id="confirm-title">تأیید عملیات</h3>
        </div>
        <div class="setia-modal-body">
            <p id="confirm-message">آیا از انجام این عملیات اطمینان دارید؟</p>
        </div>
        <div class="setia-modal-footer">
            <button id="confirm-yes" class="setia-btn setia-btn-danger">تأیید</button>
            <button id="confirm-no" class="setia-btn setia-btn-outline">انصراف</button>
        </div>
    </div>
</div>

<!-- WordPress Nonce for Security -->
<input type="hidden" id="setia-nonce" value="<?php echo wp_create_nonce('setia-history-nonce'); ?>">

<script>
// Global variables for the history page
window.setiaHistory = {
    currentPage: 1,
    itemsPerPage: 25,
    totalItems: 0,
    currentFilters: {},
    sortBy: 'created_at',
    sortOrder: 'desc',
    selectedItems: [],
    ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
    nonce: '<?php echo wp_create_nonce('setia-history-nonce'); ?>'
};

// Debug logging for template
console.log('SETIA History Template: Loaded');
console.log('SETIA History Template: setiaHistory object:', window.setiaHistory);

// Merge localized data with global object if available
jQuery(document).ready(function($) {
    if (typeof setiaHistory !== 'undefined' && window.setiaHistory) {
        // Merge the localized script data with our global object
        $.extend(window.setiaHistory, setiaHistory);
        console.log('SETIA History Template: Merged setiaHistory object:', window.setiaHistory);
    }
});
</script>
