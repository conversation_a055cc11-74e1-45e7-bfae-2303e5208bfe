<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست و بهینه‌سازی AJAX - SETIA</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f0f0f1;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #6244EC 0%, #428df5 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            position: relative;
        }
        
        .test-card h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #6244EC;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-pending { background: #ffc107; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #fd7e14; }
        
        .test-results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            line-height: 1.4;
        }
        
        .test-button {
            background: #6244EC;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px 5px 5px 0;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .test-button:hover {
            background: #5234DB;
            transform: translateY(-1px);
        }
        
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .test-button.loading {
            background: #6c757d;
            cursor: wait;
        }
        
        .test-button.loading::after {
            content: '';
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .performance-metrics {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid #ddd;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: bold;
            color: #1976d2;
        }
        
        .error-details {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #c62828;
        }
        
        .success-details {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #2e7d32;
        }
        
        .overall-status {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-weight: bold;
            z-index: 9999;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .overall-status.error {
            background: #dc3545;
        }
        
        .overall-status.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .ajax-config {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .config-item {
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .config-label {
            font-weight: bold;
            color: #856404;
        }
        
        .config-value {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="overall-status" id="overallStatus">در حال آماده‌سازی...</div>
    
    <div class="container">
        <div class="header">
            <h1>🚀 تست و بهینه‌سازی سیستم AJAX</h1>
            <p>بررسی جامع عملکرد، سرعت و قابلیت اطمینان درخواست‌های AJAX</p>
        </div>
        
        <div class="content">
            <!-- تنظیمات AJAX -->
            <div class="ajax-config">
                <h3>⚙️ تنظیمات AJAX</h3>
                <div class="config-item">
                    <span class="config-label">URL پایه:</span>
                    <span class="config-value" id="baseUrl">در حال بارگذاری...</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Timeout پیش‌فرض:</span>
                    <span class="config-value" id="defaultTimeout">30000ms</span>
                </div>
                <div class="config-item">
                    <span class="config-label">تعداد تلاش مجدد:</span>
                    <span class="config-value" id="retryCount">3</span>
                </div>
                <div class="config-item">
                    <span class="config-label">وضعیت Nonce:</span>
                    <span class="config-value" id="nonceStatus">در حال بررسی...</span>
                </div>
            </div>
            
            <div class="test-grid">
                <!-- تست اتصال پایه -->
                <div class="test-card">
                    <h3>
                        <span class="status-indicator status-pending" id="connectionStatus"></span>
                        تست اتصال پایه
                    </h3>
                    <p>بررسی اتصال به سرور WordPress و دریافت Nonce</p>
                    <button class="test-button" onclick="testBasicConnection()">
                        🔗 تست اتصال
                    </button>
                    <div class="test-results" id="connectionResults"></div>
                </div>
                
                <!-- تست عملکرد AJAX -->
                <div class="test-card">
                    <h3>
                        <span class="status-indicator status-pending" id="performanceStatus"></span>
                        تست عملکرد
                    </h3>
                    <p>اندازه‌گیری سرعت و زمان پاسخ درخواست‌های AJAX</p>
                    <button class="test-button" onclick="testPerformance()">
                        ⚡ تست عملکرد
                    </button>
                    <div class="performance-metrics" id="performanceMetrics" style="display: none;"></div>
                    <div class="test-results" id="performanceResults"></div>
                </div>
                
                <!-- تست قابلیت اطمینان -->
                <div class="test-card">
                    <h3>
                        <span class="status-indicator status-pending" id="reliabilityStatus"></span>
                        تست قابلیت اطمینان
                    </h3>
                    <p>بررسی پایداری و مدیریت خطا در شرایط مختلف</p>
                    <button class="test-button" onclick="testReliability()">
                        🛡️ تست اطمینان
                    </button>
                    <div class="test-results" id="reliabilityResults"></div>
                </div>
                
                <!-- تست امنیت -->
                <div class="test-card">
                    <h3>
                        <span class="status-indicator status-pending" id="securityStatus"></span>
                        تست امنیت
                    </h3>
                    <p>بررسی اعتبارسنجی Nonce و مدیریت دسترسی</p>
                    <button class="test-button" onclick="testSecurity()">
                        🔒 تست امنیت
                    </button>
                    <div class="test-results" id="securityResults"></div>
                </div>
                
                <!-- تست بارگذاری همزمان -->
                <div class="test-card">
                    <h3>
                        <span class="status-indicator status-pending" id="concurrencyStatus"></span>
                        تست بارگذاری همزمان
                    </h3>
                    <p>بررسی عملکرد تحت بار و درخواست‌های همزمان</p>
                    <button class="test-button" onclick="testConcurrency()">
                        🔄 تست همزمانی
                    </button>
                    <div class="test-results" id="concurrencyResults"></div>
                </div>
                
                <!-- تست بهینه‌سازی -->
                <div class="test-card">
                    <h3>
                        <span class="status-indicator status-pending" id="optimizationStatus"></span>
                        پیشنهادات بهینه‌سازی
                    </h3>
                    <p>تحلیل و ارائه راهکارهای بهبود عملکرد</p>
                    <button class="test-button" onclick="generateOptimizationSuggestions()">
                        💡 تحلیل بهینه‌سازی
                    </button>
                    <div class="test-results" id="optimizationResults"></div>
                </div>
            </div>
            
            <!-- دکمه‌های کنترل کلی -->
            <div style="text-align: center; margin: 30px 0;">
                <button class="test-button" onclick="runAllTests()" style="background: #28a745; font-size: 16px; padding: 15px 30px;">
                    🚀 اجرای تمام تست‌ها
                </button>
                <button class="test-button" onclick="clearAllResults()" style="background: #6c757d;">
                    🗑️ پاک کردن نتایج
                </button>
                <button class="test-button" onclick="exportResults()" style="background: #17a2b8;">
                    📊 خروجی گزارش
                </button>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // متغیرهای سراسری
        let testResults = {};
        let performanceData = {};
        let ajaxConfig = {
            baseUrl: '/WP/wp-admin/admin-ajax.php',
            timeout: 30000,
            retryCount: 3,
            nonce: null
        };
        
        // آماده‌سازی اولیه
        $(document).ready(function() {
            console.log('🚀 سیستم تست AJAX آماده شد');
            initializeAjaxTesting();
        });
        
        function initializeAjaxTesting() {
            updateOverallStatus('در حال آماده‌سازی...', 'warning');
            
            // به‌روزرسانی تنظیمات
            $('#baseUrl').text(ajaxConfig.baseUrl);
            $('#defaultTimeout').text(ajaxConfig.timeout + 'ms');
            $('#retryCount').text(ajaxConfig.retryCount);
            
            // تست اولیه اتصال
            setTimeout(() => {
                testBasicConnection();
            }, 1000);
        }
        
        function updateOverallStatus(message, type = 'success') {
            const statusEl = $('#overallStatus');
            statusEl.text(message);
            statusEl.removeClass('error warning').addClass(type);
        }
        
        function updateStatusIndicator(elementId, status) {
            $(`#${elementId}`).removeClass('status-pending status-success status-error status-warning')
                              .addClass(`status-${status}`);
        }
        
        function addResult(containerId, message, type = 'info') {
            const container = $(`#${containerId}`);
            const timestamp = new Date().toLocaleTimeString('fa-IR');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            container.append(`${timestamp} ${icon} ${message}\n`);
            container.scrollTop(container[0].scrollHeight);
        }
        
        // تست اتصال پایه
        async function testBasicConnection() {
            const button = event?.target;
            if (button) {
                $(button).addClass('loading').prop('disabled', true);
            }
            
            addResult('connectionResults', 'شروع تست اتصال پایه...', 'info');
            
            try {
                // تست دسترسی به admin-ajax.php
                const startTime = performance.now();
                
                const response = await $.ajax({
                    url: ajaxConfig.baseUrl,
                    type: 'POST',
                    timeout: 10000,
                    data: {
                        action: 'heartbeat',
                        _wpnonce: 'test'
                    }
                });
                
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                addResult('connectionResults', `اتصال برقرار شد - زمان پاسخ: ${responseTime}ms`, 'success');
                updateStatusIndicator('connectionStatus', 'success');
                
                // ذخیره نتیجه
                testResults.connection = {
                    success: true,
                    responseTime: responseTime,
                    timestamp: new Date().toISOString()
                };
                
                // تست دریافت nonce
                await testNonceRetrieval();
                
            } catch (error) {
                addResult('connectionResults', `خطا در اتصال: ${error.message || error}`, 'error');
                updateStatusIndicator('connectionStatus', 'error');
                
                testResults.connection = {
                    success: false,
                    error: error.message || error,
                    timestamp: new Date().toISOString()
                };
            } finally {
                if (button) {
                    $(button).removeClass('loading').prop('disabled', false);
                }
            }
        }
        
        async function testNonceRetrieval() {
            addResult('connectionResults', 'تست دریافت Nonce...', 'info');
            
            try {
                // شبیه‌سازی دریافت nonce از صفحه ادمین
                const adminResponse = await $.get('/WP/wp-admin/admin.php?page=setia-content-generator');
                
                // جستجوی nonce در پاسخ
                const nonceMatch = adminResponse.match(/setiaParams[^}]*nonce['"]\s*:\s*['"]([^'"]+)['"]/);
                
                if (nonceMatch && nonceMatch[1]) {
                    ajaxConfig.nonce = nonceMatch[1];
                    addResult('connectionResults', `Nonce دریافت شد: ${ajaxConfig.nonce.substring(0, 10)}...`, 'success');
                    $('#nonceStatus').text('✅ فعال').css('color', '#28a745');
                } else {
                    addResult('connectionResults', 'Nonce یافت نشد - استفاده از nonce تست', 'warning');
                    ajaxConfig.nonce = 'test_nonce';
                    $('#nonceStatus').text('⚠️ تست').css('color', '#ffc107');
                }
                
            } catch (error) {
                addResult('connectionResults', `خطا در دریافت Nonce: ${error.message}`, 'error');
                $('#nonceStatus').text('❌ خطا').css('color', '#dc3545');
            }
        }
        
        // تست عملکرد
        async function testPerformance() {
            const button = event?.target;
            if (button) {
                $(button).addClass('loading').prop('disabled', true);
            }

            addResult('performanceResults', 'شروع تست عملکرد...', 'info');
            $('#performanceMetrics').show();

            const metrics = {
                totalRequests: 10,
                successfulRequests: 0,
                failedRequests: 0,
                averageResponseTime: 0,
                minResponseTime: Infinity,
                maxResponseTime: 0,
                responseTimes: []
            };

            try {
                for (let i = 1; i <= metrics.totalRequests; i++) {
                    addResult('performanceResults', `درخواست ${i}/${metrics.totalRequests}...`, 'info');

                    const startTime = performance.now();

                    try {
                        await $.ajax({
                            url: ajaxConfig.baseUrl,
                            type: 'POST',
                            timeout: ajaxConfig.timeout,
                            data: {
                                action: 'setia_test_connection',
                                nonce: ajaxConfig.nonce
                            }
                        });

                        const endTime = performance.now();
                        const responseTime = endTime - startTime;

                        metrics.successfulRequests++;
                        metrics.responseTimes.push(responseTime);
                        metrics.minResponseTime = Math.min(metrics.minResponseTime, responseTime);
                        metrics.maxResponseTime = Math.max(metrics.maxResponseTime, responseTime);

                    } catch (error) {
                        metrics.failedRequests++;
                        addResult('performanceResults', `درخواست ${i} ناموفق: ${error.message}`, 'error');
                    }

                    // تاخیر کوتاه بین درخواست‌ها
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // محاسبه میانگین
                if (metrics.responseTimes.length > 0) {
                    metrics.averageResponseTime = metrics.responseTimes.reduce((a, b) => a + b, 0) / metrics.responseTimes.length;
                }

                // نمایش نتایج
                displayPerformanceMetrics(metrics);

                const successRate = (metrics.successfulRequests / metrics.totalRequests) * 100;

                if (successRate >= 90) {
                    updateStatusIndicator('performanceStatus', 'success');
                    addResult('performanceResults', `عملکرد عالی - نرخ موفقیت: ${successRate.toFixed(1)}%`, 'success');
                } else if (successRate >= 70) {
                    updateStatusIndicator('performanceStatus', 'warning');
                    addResult('performanceResults', `عملکرد قابل قبول - نرخ موفقیت: ${successRate.toFixed(1)}%`, 'warning');
                } else {
                    updateStatusIndicator('performanceStatus', 'error');
                    addResult('performanceResults', `عملکرد ضعیف - نرخ موفقیت: ${successRate.toFixed(1)}%`, 'error');
                }

                testResults.performance = metrics;

            } catch (error) {
                addResult('performanceResults', `خطا در تست عملکرد: ${error.message}`, 'error');
                updateStatusIndicator('performanceStatus', 'error');
            } finally {
                if (button) {
                    $(button).removeClass('loading').prop('disabled', false);
                }
            }
        }

        function displayPerformanceMetrics(metrics) {
            const metricsHtml = `
                <div class="metric-item">
                    <span>تعداد کل درخواست‌ها:</span>
                    <span class="metric-value">${metrics.totalRequests}</span>
                </div>
                <div class="metric-item">
                    <span>درخواست‌های موفق:</span>
                    <span class="metric-value">${metrics.successfulRequests}</span>
                </div>
                <div class="metric-item">
                    <span>درخواست‌های ناموفق:</span>
                    <span class="metric-value">${metrics.failedRequests}</span>
                </div>
                <div class="metric-item">
                    <span>میانگین زمان پاسخ:</span>
                    <span class="metric-value">${Math.round(metrics.averageResponseTime)}ms</span>
                </div>
                <div class="metric-item">
                    <span>کمترین زمان پاسخ:</span>
                    <span class="metric-value">${Math.round(metrics.minResponseTime)}ms</span>
                </div>
                <div class="metric-item">
                    <span>بیشترین زمان پاسخ:</span>
                    <span class="metric-value">${Math.round(metrics.maxResponseTime)}ms</span>
                </div>
                <div class="metric-item">
                    <span>نرخ موفقیت:</span>
                    <span class="metric-value">${((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1)}%</span>
                </div>
            `;

            $('#performanceMetrics').html(metricsHtml);
        }

        // تست قابلیت اطمینان
        async function testReliability() {
            const button = event?.target;
            if (button) {
                $(button).addClass('loading').prop('disabled', true);
            }

            addResult('reliabilityResults', 'شروع تست قابلیت اطمینان...', 'info');

            const tests = [
                { name: 'تست Timeout', test: testTimeout },
                { name: 'تست درخواست نامعتبر', test: testInvalidRequest },
                { name: 'تست بازیابی خطا', test: testErrorRecovery },
                { name: 'تست درخواست بزرگ', test: testLargeRequest }
            ];

            let passedTests = 0;

            try {
                for (const testCase of tests) {
                    addResult('reliabilityResults', `اجرای ${testCase.name}...`, 'info');

                    try {
                        await testCase.test();
                        addResult('reliabilityResults', `✅ ${testCase.name} موفق`, 'success');
                        passedTests++;
                    } catch (error) {
                        addResult('reliabilityResults', `❌ ${testCase.name} ناموفق: ${error.message}`, 'error');
                    }
                }

                const reliabilityScore = (passedTests / tests.length) * 100;

                if (reliabilityScore >= 80) {
                    updateStatusIndicator('reliabilityStatus', 'success');
                    addResult('reliabilityResults', `قابلیت اطمینان بالا - امتیاز: ${reliabilityScore}%`, 'success');
                } else if (reliabilityScore >= 60) {
                    updateStatusIndicator('reliabilityStatus', 'warning');
                    addResult('reliabilityResults', `قابلیت اطمینان متوسط - امتیاز: ${reliabilityScore}%`, 'warning');
                } else {
                    updateStatusIndicator('reliabilityStatus', 'error');
                    addResult('reliabilityResults', `قابلیت اطمینان پایین - امتیاز: ${reliabilityScore}%`, 'error');
                }

                testResults.reliability = {
                    score: reliabilityScore,
                    passedTests: passedTests,
                    totalTests: tests.length
                };

            } catch (error) {
                addResult('reliabilityResults', `خطا در تست قابلیت اطمینان: ${error.message}`, 'error');
                updateStatusIndicator('reliabilityStatus', 'error');
            } finally {
                if (button) {
                    $(button).removeClass('loading').prop('disabled', false);
                }
            }
        }

        async function testTimeout() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: ajaxConfig.baseUrl,
                    type: 'POST',
                    timeout: 1000, // timeout کوتاه
                    data: {
                        action: 'setia_test_connection',
                        nonce: ajaxConfig.nonce,
                        delay: 2000 // تاخیر بیشتر از timeout
                    }
                }).done(() => {
                    reject(new Error('درخواست نباید موفق می‌شد'));
                }).fail((xhr, status) => {
                    if (status === 'timeout') {
                        resolve('Timeout به درستی کار کرد');
                    } else {
                        reject(new Error('نوع خطای غیرمنتظره: ' + status));
                    }
                });
            });
        }

        async function testInvalidRequest() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: ajaxConfig.baseUrl,
                    type: 'POST',
                    data: {
                        action: 'invalid_action_name_12345',
                        nonce: ajaxConfig.nonce
                    }
                }).done(() => {
                    reject(new Error('درخواست نامعتبر نباید موفق می‌شد'));
                }).fail(() => {
                    resolve('درخواست نامعتبر به درستی رد شد');
                });
            });
        }

        async function testErrorRecovery() {
            // تست بازیابی پس از خطا
            let errorOccurred = false;

            try {
                await $.ajax({
                    url: ajaxConfig.baseUrl + '_invalid',
                    type: 'POST',
                    data: { action: 'test' }
                });
            } catch (error) {
                errorOccurred = true;
            }

            if (!errorOccurred) {
                throw new Error('خطا رخ نداد');
            }

            // تست درخواست معتبر پس از خطا
            await $.ajax({
                url: ajaxConfig.baseUrl,
                type: 'POST',
                data: {
                    action: 'setia_test_connection',
                    nonce: ajaxConfig.nonce
                }
            });

            return 'بازیابی پس از خطا موفق';
        }

        async function testLargeRequest() {
            const largeData = 'x'.repeat(10000); // 10KB داده

            await $.ajax({
                url: ajaxConfig.baseUrl,
                type: 'POST',
                data: {
                    action: 'setia_test_connection',
                    nonce: ajaxConfig.nonce,
                    large_data: largeData
                }
            });

            return 'درخواست بزرگ موفق';
        }

        // تست امنیت
        async function testSecurity() {
            const button = event?.target;
            if (button) {
                $(button).addClass('loading').prop('disabled', true);
            }

            addResult('securityResults', 'شروع تست امنیت...', 'info');

            const securityTests = [
                { name: 'تست Nonce معتبر', test: testValidNonce },
                { name: 'تست Nonce نامعتبر', test: testInvalidNonce },
                { name: 'تست بدون Nonce', test: testWithoutNonce },
                { name: 'تست CSRF Protection', test: testCSRFProtection }
            ];

            let passedTests = 0;

            try {
                for (const testCase of securityTests) {
                    addResult('securityResults', `اجرای ${testCase.name}...`, 'info');

                    try {
                        await testCase.test();
                        addResult('securityResults', `✅ ${testCase.name} موفق`, 'success');
                        passedTests++;
                    } catch (error) {
                        addResult('securityResults', `❌ ${testCase.name} ناموفق: ${error.message}`, 'error');
                    }
                }

                const securityScore = (passedTests / securityTests.length) * 100;

                if (securityScore >= 75) {
                    updateStatusIndicator('securityStatus', 'success');
                    addResult('securityResults', `امنیت مناسب - امتیاز: ${securityScore}%`, 'success');
                } else if (securityScore >= 50) {
                    updateStatusIndicator('securityStatus', 'warning');
                    addResult('securityResults', `امنیت متوسط - امتیاز: ${securityScore}%`, 'warning');
                } else {
                    updateStatusIndicator('securityStatus', 'error');
                    addResult('securityResults', `امنیت ضعیف - امتیاز: ${securityScore}%`, 'error');
                }

                testResults.security = {
                    score: securityScore,
                    passedTests: passedTests,
                    totalTests: securityTests.length
                };

            } catch (error) {
                addResult('securityResults', `خطا در تست امنیت: ${error.message}`, 'error');
                updateStatusIndicator('securityStatus', 'error');
            } finally {
                if (button) {
                    $(button).removeClass('loading').prop('disabled', false);
                }
            }
        }

        async function testValidNonce() {
            await $.ajax({
                url: ajaxConfig.baseUrl,
                type: 'POST',
                data: {
                    action: 'setia_test_connection',
                    nonce: ajaxConfig.nonce
                }
            });
            return 'Nonce معتبر پذیرفته شد';
        }

        async function testInvalidNonce() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: ajaxConfig.baseUrl,
                    type: 'POST',
                    data: {
                        action: 'setia_test_connection',
                        nonce: 'invalid_nonce_12345'
                    }
                }).done(() => {
                    reject(new Error('Nonce نامعتبر نباید پذیرفته می‌شد'));
                }).fail(() => {
                    resolve('Nonce نامعتبر به درستی رد شد');
                });
            });
        }

        async function testWithoutNonce() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: ajaxConfig.baseUrl,
                    type: 'POST',
                    data: {
                        action: 'setia_test_connection'
                        // بدون nonce
                    }
                }).done(() => {
                    reject(new Error('درخواست بدون nonce نباید موفق می‌شد'));
                }).fail(() => {
                    resolve('درخواست بدون nonce به درستی رد شد');
                });
            });
        }

        async function testCSRFProtection() {
            // تست محافظت در برابر CSRF
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: ajaxConfig.baseUrl,
                    type: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Referer': 'http://malicious-site.com'
                    },
                    data: {
                        action: 'setia_test_connection',
                        nonce: ajaxConfig.nonce
                    }
                }).done((response) => {
                    // اگر سرور درخواست را پذیرفت، بررسی کنیم که آیا محافظت CSRF وجود دارد
                    resolve('محافظت CSRF فعال است');
                }).fail(() => {
                    resolve('محافظت CSRF درخواست مشکوک را رد کرد');
                });
            });
        }

        // تست بارگذاری همزمان
        async function testConcurrency() {
            const button = event?.target;
            if (button) {
                $(button).addClass('loading').prop('disabled', true);
            }

            addResult('concurrencyResults', 'شروع تست بارگذاری همزمان...', 'info');

            const concurrentRequests = 5;
            const promises = [];

            try {
                addResult('concurrencyResults', `ارسال ${concurrentRequests} درخواست همزمان...`, 'info');

                for (let i = 1; i <= concurrentRequests; i++) {
                    const promise = $.ajax({
                        url: ajaxConfig.baseUrl,
                        type: 'POST',
                        timeout: ajaxConfig.timeout,
                        data: {
                            action: 'setia_test_connection',
                            nonce: ajaxConfig.nonce,
                            request_id: i
                        }
                    }).then(response => ({
                        success: true,
                        requestId: i,
                        response: response
                    })).catch(error => ({
                        success: false,
                        requestId: i,
                        error: error.message
                    }));

                    promises.push(promise);
                }

                const results = await Promise.all(promises);

                const successfulRequests = results.filter(r => r.success).length;
                const failedRequests = results.filter(r => !r.success).length;

                addResult('concurrencyResults', `نتایج: ${successfulRequests} موفق، ${failedRequests} ناموفق`, 'info');

                const concurrencyScore = (successfulRequests / concurrentRequests) * 100;

                if (concurrencyScore >= 80) {
                    updateStatusIndicator('concurrencyStatus', 'success');
                    addResult('concurrencyResults', `عملکرد همزمانی عالی - امتیاز: ${concurrencyScore}%`, 'success');
                } else if (concurrencyScore >= 60) {
                    updateStatusIndicator('concurrencyStatus', 'warning');
                    addResult('concurrencyResults', `عملکرد همزمانی متوسط - امتیاز: ${concurrencyScore}%`, 'warning');
                } else {
                    updateStatusIndicator('concurrencyStatus', 'error');
                    addResult('concurrencyResults', `عملکرد همزمانی ضعیف - امتیاز: ${concurrencyScore}%`, 'error');
                }

                testResults.concurrency = {
                    score: concurrencyScore,
                    successfulRequests: successfulRequests,
                    failedRequests: failedRequests,
                    totalRequests: concurrentRequests
                };

            } catch (error) {
                addResult('concurrencyResults', `خطا در تست همزمانی: ${error.message}`, 'error');
                updateStatusIndicator('concurrencyStatus', 'error');
            } finally {
                if (button) {
                    $(button).removeClass('loading').prop('disabled', false);
                }
            }
        }

        // تولید پیشنهادات بهینه‌سازی
        function generateOptimizationSuggestions() {
            const button = event?.target;
            if (button) {
                $(button).addClass('loading').prop('disabled', true);
            }

            addResult('optimizationResults', 'تحلیل نتایج و تولید پیشنهادات...', 'info');

            const suggestions = [];

            // تحلیل عملکرد
            if (testResults.performance) {
                const avgTime = testResults.performance.averageResponseTime;
                if (avgTime > 2000) {
                    suggestions.push('⚡ زمان پاسخ بالا - پیشنهاد: بهینه‌سازی کوئری‌های دیتابیس');
                }
                if (testResults.performance.failedRequests > 0) {
                    suggestions.push('🔧 درخواست‌های ناموفق - پیشنهاد: بهبود مدیریت خطا');
                }
            }

            // تحلیل قابلیت اطمینان
            if (testResults.reliability && testResults.reliability.score < 80) {
                suggestions.push('🛡️ قابلیت اطمینان پایین - پیشنهاد: افزودن retry mechanism');
            }

            // تحلیل امنیت
            if (testResults.security && testResults.security.score < 75) {
                suggestions.push('🔒 امنیت ضعیف - پیشنهاد: تقویت اعتبارسنجی nonce');
            }

            // تحلیل همزمانی
            if (testResults.concurrency && testResults.concurrency.score < 80) {
                suggestions.push('🔄 عملکرد همزمانی ضعیف - پیشنهاد: بهینه‌سازی سرور');
            }

            // پیشنهادات عمومی
            suggestions.push('📊 افزودن سیستم مانیتورینگ عملکرد');
            suggestions.push('💾 پیاده‌سازی کش برای درخواست‌های تکراری');
            suggestions.push('🔄 افزودن loading states بهتر');
            suggestions.push('📱 بهینه‌سازی برای دستگاه‌های موبایل');

            // نمایش پیشنهادات
            suggestions.forEach((suggestion, index) => {
                setTimeout(() => {
                    addResult('optimizationResults', suggestion, 'info');
                }, index * 200);
            });

            setTimeout(() => {
                updateStatusIndicator('optimizationStatus', 'success');
                addResult('optimizationResults', `✅ ${suggestions.length} پیشنهاد بهینه‌سازی تولید شد`, 'success');

                if (button) {
                    $(button).removeClass('loading').prop('disabled', false);
                }
            }, suggestions.length * 200 + 500);
        }

        // اجرای تمام تست‌ها
        async function runAllTests() {
            updateOverallStatus('در حال اجرای تست‌ها...', 'warning');

            const tests = [
                { name: 'اتصال پایه', func: testBasicConnection },
                { name: 'عملکرد', func: testPerformance },
                { name: 'قابلیت اطمینان', func: testReliability },
                { name: 'امنیت', func: testSecurity },
                { name: 'بارگذاری همزمان', func: testConcurrency }
            ];

            for (const test of tests) {
                try {
                    await test.func();
                    await new Promise(resolve => setTimeout(resolve, 1000)); // تاخیر بین تست‌ها
                } catch (error) {
                    console.error(`خطا در تست ${test.name}:`, error);
                }
            }

            // تولید پیشنهادات
            generateOptimizationSuggestions();

            // به‌روزرسانی وضعیت کلی
            setTimeout(() => {
                const allTestsPassed = Object.values(testResults).every(result =>
                    result.success !== false && (result.score || 0) >= 70
                );

                if (allTestsPassed) {
                    updateOverallStatus('✅ همه تست‌ها موفق', 'success');
                } else {
                    updateOverallStatus('⚠️ برخی تست‌ها نیاز به بهبود دارند', 'warning');
                }
            }, 2000);
        }

        // پاک کردن نتایج
        function clearAllResults() {
            $('.test-results').empty();
            $('.performance-metrics').hide();
            $('.status-indicator').removeClass('status-success status-error status-warning').addClass('status-pending');
            testResults = {};
            performanceData = {};
            updateOverallStatus('آماده برای تست جدید', 'success');
        }

        // خروجی گزارش
        function exportResults() {
            const report = {
                timestamp: new Date().toISOString(),
                testResults: testResults,
                summary: {
                    totalTests: Object.keys(testResults).length,
                    passedTests: Object.values(testResults).filter(r => r.success !== false).length
                }
            };

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `setia-ajax-test-report-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            updateOverallStatus('📊 گزارش دانلود شد', 'success');
        }
    </script>
</body>
</html>
