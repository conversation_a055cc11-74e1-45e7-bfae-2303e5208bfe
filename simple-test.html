<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Tab Test</title>
    <style>
        .tab-button {
            padding: 10px 20px;
            margin: 5px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            cursor: pointer;
        }
        .tab-button.active {
            background: #007cba;
            color: white;
        }
        .tab-pane {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            margin-top: 10px;
        }
        .tab-pane.active {
            display: block;
        }
        .debug {
            position: fixed;
            top: 10px;
            left: 10px;
            background: black;
            color: lime;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
            max-height: 200px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div id="debug" class="debug"></div>
    
    <h1>Simple Tab Test</h1>
    
    <div>
        <button class="tab-button active" data-tab="tab1">Tab 1</button>
        <button class="tab-button" data-tab="tab2">Tab 2</button>
        <button class="tab-button" data-tab="tab3">Tab 3</button>
    </div>
    
    <div id="tab1" class="tab-pane active">
        <h2>Tab 1 Content</h2>
        <p>This is tab 1</p>
        <button id="test-btn">Test Button</button>
    </div>
    
    <div id="tab2" class="tab-pane">
        <h2>Tab 2 Content</h2>
        <p>This is tab 2</p>
    </div>
    
    <div id="tab3" class="tab-pane">
        <h2>Tab 3 Content</h2>
        <p>This is tab 3</p>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function log(msg) {
            const debug = document.getElementById('debug');
            const time = new Date().toLocaleTimeString();
            debug.innerHTML += `<div>[${time}] ${msg}</div>`;
            debug.scrollTop = debug.scrollHeight;
            console.log(msg);
        }

        log('Starting simple test...');
        log('jQuery loaded: ' + (typeof $ !== 'undefined'));

        $(document).ready(function() {
            log('DOM ready');
            
            // Test basic jQuery
            log('Tab buttons found: ' + $('.tab-button').length);
            
            // Simple tab switching
            $('.tab-button').click(function() {
                const tabId = $(this).data('tab');
                log('Tab clicked: ' + tabId);
                
                // Remove active from all
                $('.tab-button').removeClass('active');
                $('.tab-pane').removeClass('active');
                
                // Add active to clicked
                $(this).addClass('active');
                $('#' + tabId).addClass('active');
                
                log('Switched to: ' + tabId);
            });
            
            // Test button
            $('#test-btn').click(function() {
                log('Test button clicked!');
                alert('Test button works!');
            });
            
            log('Simple test initialized');
        });
    </script>
</body>
</html>
