<?php
/**
 * تست فعال‌سازی پلاگین SETIA
 * این فایل برای بررسی مشکلات فعال‌سازی پلاگین استفاده می‌شود
 */

// جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit('Direct access not allowed');
}

echo "<h2>🔍 تست فعال‌سازی پلاگین SETIA Content Generator</h2>";

// بررسی وجود فایل اصلی پلاگین
$plugin_file = __DIR__ . '/setia-content-generator.php';

if (!file_exists($plugin_file)) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
    echo "❌ <strong>خطا:</strong> فایل اصلی پلاگین یافت نشد: " . $plugin_file;
    echo "</div>";
    exit;
}

echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
echo "✅ فایل اصلی پلاگین موجود است";
echo "</div>";

// بررسی syntax فایل PHP
echo "<h3>🔍 بررسی Syntax فایل PHP</h3>";

// خواندن محتوای فایل
$content = file_get_contents($plugin_file);

// بررسی‌های اولیه
$checks = [
    'شروع PHP' => strpos($content, '<?php') === 0,
    'پایان کلاس' => strpos($content, '} // پایان کلاس') !== false,
    'راه‌اندازی افزونه' => strpos($content, '$setia_content_generator = new SETIA_Content_Generator()') !== false,
    'تعریف کلاس' => strpos($content, 'class SETIA_Content_Generator') !== false,
];

foreach ($checks as $check_name => $result) {
    $status = $result ? "✅" : "❌";
    $color = $result ? "green" : "red";
    echo "<div style='color: $color; margin: 5px 0;'>";
    echo "$status <strong>$check_name:</strong> " . ($result ? "موجود" : "مفقود");
    echo "</div>";
}

// بررسی تعداد آکولادهای باز و بسته
$open_braces = substr_count($content, '{');
$close_braces = substr_count($content, '}');

echo "<h3>🔍 بررسی آکولادها</h3>";
echo "<div style='margin: 10px 0;'>";
echo "<strong>آکولادهای باز:</strong> $open_braces<br>";
echo "<strong>آکولادهای بسته:</strong> $close_braces<br>";

if ($open_braces === $close_braces) {
    echo "<div style='color: green;'>✅ تعداد آکولادها متعادل است</div>";
} else {
    echo "<div style='color: red;'>❌ تعداد آکولادها نامتعادل است (اختلاف: " . abs($open_braces - $close_braces) . ")</div>";
}
echo "</div>";

// بررسی وجود فایل‌های مورد نیاز
echo "<h3>🔍 بررسی فایل‌های مورد نیاز</h3>";

$required_files = [
    'ajax-handlers.php' => 'فایل مدیریت AJAX',
    'templates/main-page.php' => 'صفحه اصلی',
    'templates/history-page.php' => 'صفحه تاریخچه',
    'assets/css/admin.css' => 'فایل CSS ادمین',
    'assets/js/admin.js' => 'فایل JavaScript ادمین'
];

foreach ($required_files as $file => $description) {
    $file_path = __DIR__ . '/' . $file;
    $exists = file_exists($file_path);
    $status = $exists ? "✅" : "❌";
    $color = $exists ? "green" : "red";
    
    echo "<div style='color: $color; margin: 5px 0;'>";
    echo "$status <strong>$description:</strong> " . ($exists ? "موجود" : "مفقود") . " ($file)";
    echo "</div>";
}

// بررسی فایل‌های حذف شده (نباید وجود داشته باشند)
echo "<h3>🔍 بررسی فایل‌های حذف شده</h3>";

$deleted_files = [
    'templates/settings-page.php' => 'صفحه تنظیمات',
    'assets/css/admin-settings.css' => 'CSS تنظیمات',
    'assets/js/settings-enhanced.js' => 'JavaScript تنظیمات'
];

foreach ($deleted_files as $file => $description) {
    $file_path = __DIR__ . '/' . $file;
    $exists = file_exists($file_path);
    $status = $exists ? "❌" : "✅";
    $color = $exists ? "red" : "green";
    
    echo "<div style='color: $color; margin: 5px 0;'>";
    echo "$status <strong>$description:</strong> " . ($exists ? "هنوز موجود است (باید حذف شود)" : "به درستی حذف شده") . " ($file)";
    echo "</div>";
}

// تلاش برای include کردن فایل
echo "<h3>🔍 تست بارگذاری فایل</h3>";

try {
    // ذخیره error reporting فعلی
    $old_error_reporting = error_reporting();
    $old_display_errors = ini_get('display_errors');
    
    // فعال کردن نمایش خطاها
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    // شروع output buffering برای گرفتن خطاها
    ob_start();
    
    // تلاش برای include کردن فایل
    include_once $plugin_file;
    
    // گرفتن خروجی
    $output = ob_get_clean();
    
    // بازگردانی تنظیمات قبلی
    error_reporting($old_error_reporting);
    ini_set('display_errors', $old_display_errors);
    
    if (empty($output)) {
        echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
        echo "✅ فایل بدون خطا بارگذاری شد";
        echo "</div>";
        
        // بررسی وجود کلاس
        if (class_exists('SETIA_Content_Generator')) {
            echo "<div style='color: green; margin: 10px 0;'>";
            echo "✅ کلاس SETIA_Content_Generator با موفقیت تعریف شد";
            echo "</div>";
        } else {
            echo "<div style='color: red; margin: 10px 0;'>";
            echo "❌ کلاس SETIA_Content_Generator تعریف نشد";
            echo "</div>";
        }
    } else {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
        echo "❌ <strong>خطا در بارگذاری فایل:</strong><br>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
    echo "❌ <strong>Exception در بارگذاری فایل:</strong><br>";
    echo htmlspecialchars($e->getMessage());
    echo "</div>";
} catch (ParseError $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
    echo "❌ <strong>Parse Error:</strong><br>";
    echo htmlspecialchars($e->getMessage()) . " در خط " . $e->getLine();
    echo "</div>";
} catch (Error $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
    echo "❌ <strong>Fatal Error:</strong><br>";
    echo htmlspecialchars($e->getMessage()) . " در خط " . $e->getLine();
    echo "</div>";
}

echo "<h3>📋 خلاصه</h3>";
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p>این تست برای شناسایی مشکلات فعال‌سازی پلاگین طراحی شده است.</p>";
echo "<p>اگر همه موارد سبز هستند، پلاگین باید قابل فعال‌سازی باشد.</p>";
echo "<p>در صورت وجود خطا، لطفاً خطاهای قرمز را بررسی و رفع کنید.</p>";
echo "</div>";
?>
