<?php
/**
 * SETIA Plugin Issues Diagnostic and Fix Script
 * این اسکریپت مشکلات سیستم زمانبندی SETIA را تشخیص و رفع می‌کند
 */

// Security check
if (!defined('ABSPATH')) {
    // Try to load WordPress
    $wp_load_paths = [
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php',
        dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
        dirname(dirname(dirname(dirname(__DIR__)))) . '/wp-load.php'
    ];
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this script from WordPress admin or ensure wp-load.php is accessible.');
    }
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('شما دسترسی کافی برای اجرای این اسکریپت ندارید.');
}

echo "<h1>🔧 SETIA Plugin Issues Diagnostic & Fix</h1>";
echo "<div style='font-family: Tahoma, Arial, sans-serif; direction: rtl; text-align: right;'>";

// 1. Check WordPress Cron Status
echo "<h2>1️⃣ بررسی وضعیت WordPress Cron</h2>";
if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
    echo "<p style='color: red;'>❌ WordPress Cron غیرفعال است (DISABLE_WP_CRON = true)</p>";
    echo "<p><strong>راه حل:</strong> در فایل wp-config.php خط زیر را حذف یا کامنت کنید:</p>";
    echo "<code>define('DISABLE_WP_CRON', true);</code>";
} else {
    echo "<p style='color: green;'>✅ WordPress Cron فعال است</p>";
}

// 2. Check Database Tables
echo "<h2>2️⃣ بررسی جداول دیتابیس</h2>";
global $wpdb;

$required_tables = [
    $wpdb->prefix . 'setia_generated_content',
    $wpdb->prefix . 'setia_content_schedules', 
    $wpdb->prefix . 'setia_scheduler_logs'
];

$missing_tables = [];
foreach ($required_tables as $table) {
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table'");
    if ($table_exists) {
        echo "<p style='color: green;'>✅ جدول $table موجود است</p>";
    } else {
        echo "<p style='color: red;'>❌ جدول $table موجود نیست</p>";
        $missing_tables[] = $table;
    }
}

// 3. Create Missing Tables
if (!empty($missing_tables)) {
    echo "<h3>🔨 ایجاد جداول مفقود...</h3>";
    
    // Load the main plugin class to access create_database_tables method
    if (class_exists('SETIA_Content_Generator')) {
        $setia_plugin = new SETIA_Content_Generator();
        
        // Use reflection to access private method
        $reflection = new ReflectionClass($setia_plugin);
        $method = $reflection->getMethod('create_database_tables');
        $method->setAccessible(true);
        
        try {
            $method->invoke($setia_plugin);
            echo "<p style='color: green;'>✅ جداول با موفقیت ایجاد شدند</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطا در ایجاد جداول: " . $e->getMessage() . "</p>";
        }
    } else {
        // Manual table creation
        create_setia_tables_manually();
    }
}

// 4. Check User Capabilities
echo "<h2>3️⃣ بررسی دسترسی‌های کاربر</h2>";
$required_caps = ['manage_options', 'edit_posts', 'publish_posts'];
foreach ($required_caps as $cap) {
    if (current_user_can($cap)) {
        echo "<p style='color: green;'>✅ دسترسی $cap موجود است</p>";
    } else {
        echo "<p style='color: red;'>❌ دسترسی $cap موجود نیست</p>";
    }
}

// 5. Check Scheduler Class
echo "<h2>4️⃣ بررسی کلاس زمانبندی</h2>";
$scheduler_file = plugin_dir_path(__FILE__) . 'includes/scheduler.php';
if (file_exists($scheduler_file)) {
    echo "<p style='color: green;'>✅ فایل scheduler.php موجود است</p>";
    
    require_once $scheduler_file;
    if (class_exists('SETIA_Scheduler')) {
        echo "<p style='color: green;'>✅ کلاس SETIA_Scheduler بارگذاری شد</p>";
    } else {
        echo "<p style='color: red;'>❌ کلاس SETIA_Scheduler یافت نشد</p>";
    }
} else {
    echo "<p style='color: red;'>❌ فایل scheduler.php موجود نیست</p>";
}

// 6. Test AJAX Endpoints
echo "<h2>5️⃣ بررسی AJAX Endpoints</h2>";
$ajax_actions = [
    'setia_get_schedules',
    'setia_save_schedule', 
    'setia_delete_schedule',
    'setia_toggle_schedule',
    'setia_run_schedule_now',
    'setia_get_schedule_logs'
];

foreach ($ajax_actions as $action) {
    if (has_action("wp_ajax_$action")) {
        echo "<p style='color: green;'>✅ AJAX action $action ثبت شده است</p>";
    } else {
        echo "<p style='color: red;'>❌ AJAX action $action ثبت نشده است</p>";
    }
}

// 7. Fix Plugin Activation
echo "<h2>6️⃣ اجرای مجدد فعال‌سازی پلاگین</h2>";
if (class_exists('SETIA_Content_Generator')) {
    $setia_plugin = new SETIA_Content_Generator();
    
    // Use reflection to access private method
    $reflection = new ReflectionClass($setia_plugin);
    $method = $reflection->getMethod('activate_plugin');
    $method->setAccessible(true);
    
    try {
        $method->invoke($setia_plugin);
        echo "<p style='color: green;'>✅ فعال‌سازی مجدد پلاگین انجام شد</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطا در فعال‌سازی مجدد: " . $e->getMessage() . "</p>";
    }
}

// 8. Clear Cache
echo "<h2>7️⃣ پاک کردن کش</h2>";
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "<p style='color: green;'>✅ کش WordPress پاک شد</p>";
}

// Update asset versions
$current_time = time();
update_option('setia_asset_version', $current_time);
update_option('setia_css_version', $current_time);
update_option('setia_js_version', $current_time);
echo "<p style='color: green;'>✅ نسخه‌های asset بروزرسانی شدند</p>";

echo "</div>";

// Function to manually create tables
function create_setia_tables_manually() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    
    // Generated content table
    $table1 = $wpdb->prefix . 'setia_generated_content';
    $sql1 = "CREATE TABLE $table1 (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        post_id mediumint(9) DEFAULT NULL,
        topic varchar(255) NOT NULL,
        keywords text NOT NULL,
        tone varchar(50) NOT NULL,
        category varchar(100) NOT NULL,
        length varchar(50) NOT NULL,
        generated_text longtext NOT NULL,
        generated_image_url varchar(255) DEFAULT NULL,
        seo_meta text DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) $charset_collate;";
    
    // Schedules table
    $table2 = $wpdb->prefix . 'setia_content_schedules';
    $sql2 = "CREATE TABLE $table2 (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        title varchar(255) NOT NULL,
        topic varchar(255) NOT NULL,
        keywords text,
        category_id mediumint(9) DEFAULT NULL,
        tone varchar(50) DEFAULT 'عادی',
        length varchar(50) DEFAULT 'متوسط',
        frequency varchar(50) NOT NULL,
        status tinyint(1) DEFAULT 1,
        daily_limit int(11) DEFAULT 1,
        generate_image tinyint(1) DEFAULT 1,
        last_run datetime DEFAULT NULL,
        next_run datetime DEFAULT NULL,
        generated_count int(11) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) $charset_collate;";
    
    // Logs table
    $table3 = $wpdb->prefix . 'setia_scheduler_logs';
    $sql3 = "CREATE TABLE $table3 (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        schedule_id mediumint(9) DEFAULT NULL,
        message text NOT NULL,
        type varchar(20) DEFAULT 'info',
        post_id mediumint(9) DEFAULT NULL,
        execution_time varchar(50) DEFAULT NULL,
        memory_usage varchar(50) DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY schedule_id (schedule_id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    
    $result1 = dbDelta($sql1);
    $result2 = dbDelta($sql2);
    $result3 = dbDelta($sql3);
    
    echo "<p style='color: green;'>✅ جداول به صورت دستی ایجاد شدند</p>";
}

echo "<h2>🎉 تشخیص و رفع مشکلات تکمیل شد!</h2>";
echo "<p><a href='" . admin_url('admin.php?page=setia-scheduler') . "'>🔗 بازگشت به صفحه زمانبندی</a></p>";
?>
