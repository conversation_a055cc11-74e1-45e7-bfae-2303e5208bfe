<?php
/**
 * SETIA Database Setup Script
 * اسکریپت راه‌اندازی دیتابیس برای پلاگین SETIA
 */

// تلاش برای بارگذاری WordPress
$wp_paths = [
    __DIR__ . '/../../../wp-load.php',
    __DIR__ . '/../../../../wp-load.php',
    __DIR__ . '/../../../../../wp-load.php'
];

$loaded = false;
foreach ($wp_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $loaded = true;
        break;
    }
}

if (!$loaded) {
    die('خطا: فایل wp-load.php یافت نشد. لطفاً این اسکریپت را از داخل WordPress اجرا کنید.');
}

// بررسی دسترسی
if (!current_user_can('manage_options')) {
    die('خطا: شما دسترسی کافی برای اجرای این اسکریپت ندارید.');
}

?>
<!DOCTYPE html>
<html dir="rtl" lang="fa">
<head>
    <meta charset="UTF-8">
    <title>راه‌اندازی دیتابیس SETIA</title>
    <style>
        body { font-family: Tahoma, Arial, sans-serif; direction: rtl; text-align: right; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .box { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .button { background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
    </style>
</head>
<body>

<h1>🔧 راه‌اندازی دیتابیس SETIA</h1>

<?php
// 1. بررسی وضعیت WordPress Cron
echo "<div class='box'>";
echo "<h2>1️⃣ بررسی WordPress Cron</h2>";
if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
    echo "<p class='error'>❌ WordPress Cron غیرفعال است</p>";
    echo "<p>برای فعال‌سازی، خط زیر را از wp-config.php حذف کنید:</p>";
    echo "<code>define('DISABLE_WP_CRON', true);</code>";
} else {
    echo "<p class='success'>✅ WordPress Cron فعال است</p>";
}
echo "</div>";

// 2. ایجاد جداول دیتابیس
echo "<div class='box'>";
echo "<h2>2️⃣ ایجاد جداول دیتابیس</h2>";

global $wpdb;
$charset_collate = $wpdb->get_charset_collate();

// جدول محتوای تولید شده
$table1 = $wpdb->prefix . 'setia_generated_content';
$sql1 = "CREATE TABLE IF NOT EXISTS $table1 (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    post_id mediumint(9) DEFAULT NULL,
    topic varchar(255) NOT NULL,
    keywords text NOT NULL,
    tone varchar(50) NOT NULL,
    category varchar(100) NOT NULL,
    length varchar(50) NOT NULL,
    generated_text longtext NOT NULL,
    generated_image_url varchar(255) DEFAULT NULL,
    seo_meta text DEFAULT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
) $charset_collate;";

// جدول زمانبندی‌ها
$table2 = $wpdb->prefix . 'setia_content_schedules';
$sql2 = "CREATE TABLE IF NOT EXISTS $table2 (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    title varchar(255) NOT NULL,
    topic varchar(255) NOT NULL,
    keywords text,
    category_id mediumint(9) DEFAULT NULL,
    tone varchar(50) DEFAULT 'عادی',
    length varchar(50) DEFAULT 'متوسط',
    frequency varchar(50) NOT NULL,
    status tinyint(1) DEFAULT 1,
    daily_limit int(11) DEFAULT 1,
    generate_image tinyint(1) DEFAULT 1,
    last_run datetime DEFAULT NULL,
    next_run datetime DEFAULT NULL,
    generated_count int(11) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
) $charset_collate;";

// جدول لاگ‌ها
$table3 = $wpdb->prefix . 'setia_scheduler_logs';
$sql3 = "CREATE TABLE IF NOT EXISTS $table3 (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    schedule_id mediumint(9) DEFAULT NULL,
    message text NOT NULL,
    type varchar(20) DEFAULT 'info',
    post_id mediumint(9) DEFAULT NULL,
    execution_time varchar(50) DEFAULT NULL,
    memory_usage varchar(50) DEFAULT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY schedule_id (schedule_id)
) $charset_collate;";

// اجرای کوئری‌ها
$results = [];
$results[] = $wpdb->query($sql1);
$results[] = $wpdb->query($sql2);
$results[] = $wpdb->query($sql3);

// بررسی نتایج
$tables = [$table1, $table2, $table3];
$table_names = ['محتوای تولید شده', 'زمانبندی‌ها', 'لاگ‌ها'];

for ($i = 0; $i < count($tables); $i++) {
    if ($wpdb->get_var("SHOW TABLES LIKE '{$tables[$i]}'")) {
        echo "<p class='success'>✅ جدول {$table_names[$i]} ایجاد شد</p>";
    } else {
        echo "<p class='error'>❌ خطا در ایجاد جدول {$table_names[$i]}</p>";
    }
}
echo "</div>";

// 3. تنظیم گزینه‌های پیش‌فرض
echo "<div class='box'>";
echo "<h2>3️⃣ تنظیم گزینه‌های پیش‌فرض</h2>";

$default_settings = [
    'gemini_api_key' => '',
    'gemma_api_key' => '',
    'imagine_art_api_key' => '',
    'default_tone' => 'عادی',
    'default_length' => 'متوسط',
    'enable_seo' => 'yes',
    'enable_image_generation' => 'yes',
    'default_image_style' => 'realistic',
    'default_aspect_ratio' => '16:9',
    'internal_cron_interval' => 15
];

$updated = update_option('setia_settings', $default_settings);
if ($updated) {
    echo "<p class='success'>✅ تنظیمات پیش‌فرض ذخیره شدند</p>";
} else {
    echo "<p class='info'>ℹ️ تنظیمات قبلاً موجود بودند</p>";
}
echo "</div>";

// 4. بررسی دسترسی‌ها
echo "<div class='box'>";
echo "<h2>4️⃣ بررسی دسترسی‌های کاربر</h2>";
$user = wp_get_current_user();
echo "<p class='info'>کاربر فعلی: {$user->display_name} (ID: {$user->ID})</p>";

$capabilities = ['manage_options', 'edit_posts', 'publish_posts'];
foreach ($capabilities as $cap) {
    if (current_user_can($cap)) {
        echo "<p class='success'>✅ دسترسی $cap موجود است</p>";
    } else {
        echo "<p class='error'>❌ دسترسی $cap موجود نیست</p>";
    }
}
echo "</div>";

// 5. پاک کردن کش
echo "<div class='box'>";
echo "<h2>5️⃣ پاک کردن کش</h2>";
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "<p class='success'>✅ کش WordPress پاک شد</p>";
}

$current_time = time();
update_option('setia_asset_version', $current_time);
update_option('setia_css_version', $current_time);
update_option('setia_js_version', $current_time);
echo "<p class='success'>✅ نسخه‌های asset بروزرسانی شدند</p>";
echo "</div>";

// 6. بررسی نهایی
echo "<div class='box'>";
echo "<h2>6️⃣ بررسی نهایی سیستم</h2>";

$final_check = [
    'cron_enabled' => !defined('DISABLE_WP_CRON') || !DISABLE_WP_CRON,
    'tables_exist' => $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}setia_content_schedules'") ? true : false,
    'permissions' => current_user_can('manage_options'),
    'settings_saved' => get_option('setia_settings') ? true : false
];

foreach ($final_check as $check => $status) {
    $label = [
        'cron_enabled' => 'WordPress Cron فعال',
        'tables_exist' => 'جداول دیتابیس موجود',
        'permissions' => 'دسترسی‌های لازم',
        'settings_saved' => 'تنظیمات ذخیره شده'
    ][$check];
    
    if ($status) {
        echo "<p class='success'>✅ $label</p>";
    } else {
        echo "<p class='error'>❌ $label</p>";
    }
}

$all_good = array_reduce($final_check, function($carry, $item) { return $carry && $item; }, true);

if ($all_good) {
    echo "<h3 class='success'>🎉 سیستم آماده استفاده است!</h3>";
} else {
    echo "<h3 class='warning'>⚠️ برخی مشکلات نیاز به رفع دارند</h3>";
}
echo "</div>";
?>

<div class='box'>
    <h2>🔗 لینک‌های مفید</h2>
    <a href="<?php echo admin_url('admin.php?page=setia-scheduler'); ?>" class="button">صفحه زمانبندی</a>
    <a href="<?php echo admin_url('admin.php?page=setia-settings'); ?>" class="button">تنظیمات</a>
    <a href="<?php echo admin_url('plugins.php'); ?>" class="button">مدیریت پلاگین‌ها</a>
</div>

</body>
</html>
