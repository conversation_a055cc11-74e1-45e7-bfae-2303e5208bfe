# 🧪 راهنمای ابزارهای تست پلاگین SETIA

این مجموعه شامل ابزارهای مختلف تست برای پلاگین SETIA Content Generator است که برای بررسی عملکرد، امنیت، و قابلیت اطمینان سیستم طراحی شده‌اند.

## 📁 فایل‌های تست موجود

### 1. `ajax-optimization-test.html` - تست جامع AJAX
**هدف**: تست کامل سیستم AJAX با تمام جزئیات
**ویژگی‌ها**:
- ✅ تست اتصال پایه و دریافت Nonce
- ⚡ تست عملکرد و سرعت پاسخ
- 🔒 تست امنیت و محافظت CSRF
- 🔄 تست قابلیت اطمینان و retry mechanism
- 🚀 تست درخواست‌های همزمان
- 📊 آمارگیری دقیق و نمایش متریک‌ها

**نحوه استفاده**:
```bash
# باز کردن در مرورگر
file:///path/to/ajax-optimization-test.html
```

### 2. `ajax-test-simple.html` - تست سریع AJAX
**هدف**: تست سریع و ساده سیستم AJAX بهینه‌سازی شده
**ویژگی‌ها**:
- 🔌 تست اتصال پایه
- 💾 تست عملکرد کش
- ⚡ تست همزمانی
- 🛡️ تست مدیریت خطا
- 📈 نمایش آمار بلادرنگ

**نحوه استفاده**:
```bash
# باز کردن در مرورگر
file:///path/to/ajax-test-simple.html
```

### 3. `remote-server-test.html` - تست سرور ریموت
**هدف**: تست مخصوص سرور ریموت tickfile.ir
**ویژگی‌ها**:
- 🌐 تست اتصال به سرور ریموت
- 🔗 تست WordPress API
- 📁 تست دسترسی به فایل‌های پلاگین
- ⚡ تست سیستم AJAX بهینه‌سازی شده
- 🔒 تست امنیت و مجوزها
- 🛠️ تست قابلیت‌های اصلی پلاگین

**آدرس سرور**: `https://tickfile.ir/wp-content/plugins/SETIA/`

## 🚀 راهنمای استفاده

### مرحله 1: آماده‌سازی
1. اطمینان از بارگذاری پلاگین روی سرور
2. فعال‌سازی پلاگین در WordPress
3. تنظیم مجوزهای مناسب فایل‌ها

### مرحله 2: تست محلی
```bash
# تست سیستم AJAX بهینه‌سازی شده
open ajax-test-simple.html

# تست جامع AJAX
open ajax-optimization-test.html
```

### مرحله 3: تست سرور ریموت
```bash
# تست سرور tickfile.ir
open remote-server-test.html
```

## 📊 تفسیر نتایج

### نشانگرهای موفقیت ✅
- **اتصال پایه**: زمان پاسخ < 2000ms
- **AJAX بهینه‌سازی شده**: بارگذاری موفق SetiaAjaxManager
- **کش**: درخواست دوم سریع‌تر از اول
- **همزمانی**: همه درخواست‌های همزمان موفق
- **امنیت**: Nonce معتبر و محافظت CSRF فعال

### نشانگرهای هشدار ⚠️
- **زمان پاسخ بالا**: > 5000ms
- **نرخ خطای بالا**: > 10%
- **مشکل کش**: عدم کاهش زمان پاسخ
- **مشکل امنیت**: عدم تشخیص Nonce نامعتبر

### نشانگرهای خطا ❌
- **عدم اتصال**: سرور در دسترس نیست
- **خطای 404**: فایل‌های پلاگین یافت نشدند
- **خطای 500**: مشکل سرور یا کد
- **خطای JavaScript**: مشکل در کد frontend

## 🔧 عیب‌یابی

### مشکلات رایج و راه‌حل‌ها

#### 1. خطای CORS
**علت**: محدودیت Cross-Origin Resource Sharing
**راه‌حل**:
```javascript
// اضافه کردن header مناسب در سرور
Access-Control-Allow-Origin: *
```

#### 2. خطای 404 در فایل‌های پلاگین
**علت**: مسیر نادرست یا عدم بارگذاری فایل‌ها
**راه‌حل**:
- بررسی مسیر فایل‌ها
- اطمینان از بارگذاری کامل پلاگین

#### 3. خطای Nonce
**علت**: عدم تولید یا انقضای Nonce
**راه‌حل**:
- بررسی تنظیمات WordPress
- تجدید نشست کاربری

#### 4. کندی سرور
**علت**: بار بالای سرور یا مشکل شبکه
**راه‌حل**:
- بررسی منابع سرور
- بهینه‌سازی کد PHP

## 📈 بهینه‌سازی عملکرد

### توصیه‌های عملکرد
1. **کش**: استفاده از کش برای درخواست‌های تکراری
2. **فشرده‌سازی**: فعال‌سازی Gzip compression
3. **CDN**: استفاده از Content Delivery Network
4. **بهینه‌سازی تصاویر**: کاهش حجم فایل‌های تصویری
5. **Lazy Loading**: بارگذاری تنبل محتوا

### مانیتورینگ مداوم
- بررسی روزانه آمار عملکرد
- تنظیم هشدارهای خودکار
- ثبت لاگ‌های مفصل
- تست منظم قابلیت‌ها

## 🔐 ملاحظات امنیتی

### نکات امنیتی مهم
1. **Nonce Validation**: همیشه Nonce را تایید کنید
2. **User Capabilities**: بررسی مجوزهای کاربر
3. **Input Sanitization**: پاک‌سازی ورودی‌های کاربر
4. **Rate Limiting**: محدودیت تعداد درخواست‌ها
5. **HTTPS**: استفاده از اتصال امن

## 📞 پشتیبانی

در صورت بروز مشکل یا نیاز به راهنمایی:
1. بررسی لاگ‌های خطا در WordPress
2. فعال‌سازی حالت Debug در WordPress
3. بررسی Console مرورگر برای خطاهای JavaScript
4. تست در مرورگرهای مختلف

---

**نکته**: این ابزارها برای توسعه‌دهندگان و مدیران سیستم طراحی شده‌اند. برای استفاده در محیط تولید، حتماً تست‌های کامل انجام دهید.
