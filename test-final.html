<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست نهایی CSS - SETIA</title>
    
    <!-- بارگذاری CSS های SETIA -->
    <link rel="stylesheet" href="assets/css/setia-fixed.css">
    <link rel="stylesheet" href="assets/css/admin.css">
    <link rel="stylesheet" href="assets/css/admin-settings.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f1;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        
        .test-status {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #00a32a;
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-weight: bold;
            z-index: 9999;
        }
        
        .test-status.error {
            background: #d63638;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #6244EC 0%, #428df5 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        
        .test-card h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #6244EC;
            padding-bottom: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-success { background: #00a32a; }
        .status-error { background: #d63638; }
        .status-warning { background: #f59e0b; }
        
        .results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        button {
            background: #6244EC;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.2s ease;
        }
        
        button:hover {
            background: #5234DB;
            transform: translateY(-1px);
        }
    </style>
</head>
<body class="wp-admin">
    <div class="test-status" id="testStatus">در حال بررسی...</div>
    
    <div class="main-container">
        <div class="header">
            <h1>🔧 تست نهایی CSS - پلاگین SETIA</h1>
            <p>بررسی کامل عملکرد CSS و متغیرها</p>
        </div>
        
        <div class="content">
            <div class="test-grid">
                <div class="test-card">
                    <h3>
                        <span class="status-indicator" id="cssStatus"></span>
                        بارگذاری CSS
                    </h3>
                    <p>بررسی بارگذاری فایل‌های CSS</p>
                    <button onclick="testCSSLoading()">تست بارگذاری</button>
                    <div class="results" id="cssResults"></div>
                </div>
                
                <div class="test-card">
                    <h3>
                        <span class="status-indicator" id="variablesStatus"></span>
                        متغیرهای CSS
                    </h3>
                    <p>بررسی متغیرهای CSS تعریف شده</p>
                    <button onclick="testCSSVariables()">تست متغیرها</button>
                    <div class="results" id="variablesResults"></div>
                </div>
                
                <div class="test-card">
                    <h3>
                        <span class="status-indicator" id="stylesStatus"></span>
                        استایل‌های SETIA
                    </h3>
                    <p>تست استایل‌های واقعی پلاگین</p>
                    <button onclick="testSetiaStyles()">تست استایل‌ها</button>
                    <div class="results" id="stylesResults"></div>
                </div>
                
                <div class="test-card">
                    <h3>
                        <span class="status-indicator" id="compatibilityStatus"></span>
                        سازگاری مرورگر
                    </h3>
                    <p>بررسی پشتیبانی ویژگی‌های CSS</p>
                    <button onclick="testBrowserCompatibility()">تست سازگاری</button>
                    <div class="results" id="compatibilityResults"></div>
                </div>
            </div>
            
            <!-- نمونه استایل‌های SETIA -->
            <div class="setia-container">
                <div class="setia-section">
                    <div class="setia-section-header">
                        <div class="setia-section-icon">🎯</div>
                        <div class="setia-section-title">
                            <h2>نمونه استایل‌های SETIA</h2>
                            <p>این بخش برای تست استایل‌های واقعی پلاگین طراحی شده است</p>
                        </div>
                    </div>
                    <div class="setia-section-content">
                        <div class="setia-form-group">
                            <label class="setia-label">
                                <span class="label-icon">🔑</span>
                                فیلد نمونه
                            </label>
                            <input type="text" class="setia-input" placeholder="متن نمونه">
                        </div>
                        
                        <div class="setia-form-group">
                            <label class="setia-label">انتخاب نمونه</label>
                            <select class="setia-select">
                                <option>گزینه اول</option>
                                <option>گزینه دوم</option>
                            </select>
                        </div>
                        
                        <button class="setia-button setia-button-primary">
                            <span class="button-icon">💾</span>
                            دکمه نمونه
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تست خودکار هنگام بارگذاری
        window.addEventListener('load', function() {
            console.log('🚀 صفحه تست نهایی بارگذاری شد');
            
            // تست خودکار
            setTimeout(() => {
                testCSSLoading();
                setTimeout(() => testCSSVariables(), 500);
                setTimeout(() => testSetiaStyles(), 1000);
                setTimeout(() => testBrowserCompatibility(), 1500);
            }, 1000);
        });
        
        function updateStatus(elementId, status) {
            const element = document.getElementById(elementId);
            element.className = 'status-indicator status-' + status;
        }
        
        function testCSSLoading() {
            const results = document.getElementById('cssResults');
            results.innerHTML = 'بررسی بارگذاری CSS...\n\n';
            
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            const setiaCSS = Array.from(cssLinks).filter(link => 
                link.href.includes('setia') || 
                link.href.includes('admin')
            );
            
            results.innerHTML += `تعداد کل CSS: ${cssLinks.length}\n`;
            results.innerHTML += `CSS های SETIA: ${setiaCSS.length}\n\n`;
            
            if (setiaCSS.length > 0) {
                results.innerHTML += 'فایل‌های CSS یافت شده:\n';
                setiaCSS.forEach((link, index) => {
                    const fileName = link.href.split('/').pop();
                    results.innerHTML += `${index + 1}. ${fileName}\n`;
                });
                updateStatus('cssStatus', 'success');
            } else {
                results.innerHTML += '❌ هیچ فایل CSS از SETIA یافت نشد\n';
                updateStatus('cssStatus', 'error');
            }
        }
        
        function testCSSVariables() {
            const results = document.getElementById('variablesResults');
            results.innerHTML = 'بررسی متغیرهای CSS...\n\n';
            
            const variables = [
                '--setia-primary',
                '--setia-secondary',
                '--setia-success',
                '--setia-error',
                '--setia-white',
                '--setia-gray-50',
                '--setia-space-4',
                '--setia-text-base',
                '--setia-radius',
                '--setia-shadow'
            ];
            
            const rootStyles = getComputedStyle(document.documentElement);
            let successCount = 0;
            
            variables.forEach(variable => {
                const value = rootStyles.getPropertyValue(variable).trim();
                const status = value ? '✅' : '❌';
                if (value) successCount++;
                results.innerHTML += `${status} ${variable}: ${value || 'تعریف نشده'}\n`;
            });
            
            const successRate = (successCount / variables.length) * 100;
            results.innerHTML += `\nنرخ موفقیت: ${successRate.toFixed(1)}%\n`;
            
            if (successRate > 80) {
                updateStatus('variablesStatus', 'success');
            } else if (successRate > 50) {
                updateStatus('variablesStatus', 'warning');
            } else {
                updateStatus('variablesStatus', 'error');
            }
        }
        
        function testSetiaStyles() {
            const results = document.getElementById('stylesResults');
            results.innerHTML = 'بررسی استایل‌های SETIA...\n\n';
            
            const elements = [
                { selector: '.setia-section', property: 'backgroundColor' },
                { selector: '.setia-input', property: 'borderRadius' },
                { selector: '.setia-button-primary', property: 'background' },
                { selector: '.setia-section-header', property: 'padding' }
            ];
            
            let successCount = 0;
            
            elements.forEach(({ selector, property }) => {
                const element = document.querySelector(selector);
                if (element) {
                    const styles = getComputedStyle(element);
                    const value = styles[property];
                    const hasValue = value && value !== 'initial' && value !== 'auto';
                    const status = hasValue ? '✅' : '❌';
                    if (hasValue) successCount++;
                    results.innerHTML += `${status} ${selector} ${property}: ${value}\n`;
                } else {
                    results.innerHTML += `❌ ${selector}: عنصر یافت نشد\n`;
                }
            });
            
            const successRate = (successCount / elements.length) * 100;
            results.innerHTML += `\nنرخ موفقیت: ${successRate.toFixed(1)}%\n`;
            
            if (successRate > 75) {
                updateStatus('stylesStatus', 'success');
            } else if (successRate > 50) {
                updateStatus('stylesStatus', 'warning');
            } else {
                updateStatus('stylesStatus', 'error');
            }
        }
        
        function testBrowserCompatibility() {
            const results = document.getElementById('compatibilityResults');
            results.innerHTML = 'بررسی سازگاری مرورگر...\n\n';
            
            const features = [
                { name: 'CSS Variables', test: () => CSS.supports('color', 'var(--test)') },
                { name: 'CSS Grid', test: () => CSS.supports('display', 'grid') },
                { name: 'CSS Flexbox', test: () => CSS.supports('display', 'flex') },
                { name: 'CSS Transforms', test: () => CSS.supports('transform', 'translateY(-2px)') },
                { name: 'CSS Transitions', test: () => CSS.supports('transition', 'all 0.2s ease') }
            ];
            
            let supportedCount = 0;
            
            features.forEach(feature => {
                const supported = feature.test();
                const status = supported ? '✅' : '❌';
                if (supported) supportedCount++;
                results.innerHTML += `${status} ${feature.name}\n`;
            });
            
            const supportRate = (supportedCount / features.length) * 100;
            results.innerHTML += `\nنرخ پشتیبانی: ${supportRate.toFixed(1)}%\n`;
            
            if (supportRate === 100) {
                updateStatus('compatibilityStatus', 'success');
            } else if (supportRate > 80) {
                updateStatus('compatibilityStatus', 'warning');
            } else {
                updateStatus('compatibilityStatus', 'error');
            }
            
            // به‌روزرسانی وضعیت کلی
            updateOverallStatus();
        }
        
        function updateOverallStatus() {
            const statusElements = document.querySelectorAll('.status-indicator');
            const successCount = document.querySelectorAll('.status-success').length;
            const totalCount = statusElements.length;
            
            const testStatus = document.getElementById('testStatus');
            
            if (successCount === totalCount) {
                testStatus.textContent = '✅ همه تست‌ها موفق';
                testStatus.className = 'test-status';
            } else if (successCount > totalCount / 2) {
                testStatus.textContent = `⚠️ ${successCount}/${totalCount} تست موفق`;
                testStatus.className = 'test-status';
            } else {
                testStatus.textContent = `❌ ${successCount}/${totalCount} تست موفق`;
                testStatus.className = 'test-status error';
            }
        }
    </script>
</body>
</html>
