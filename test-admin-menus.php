<?php
/**
 * فایل تست منوهای ادمین پلاگین SETIA
 * این فایل برای بررسی وضعیت منوها و شناسایی تکرارها استفاده می‌شود
 */

// جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

// بررسی اینکه آیا پلاگین فعال است
if (!class_exists('SETIA_Content_Generator')) {
    wp_die('پلاگین SETIA Content Generator فعال نیست!');
}

?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست منوهای ادمین SETIA</title>
    <style>
        body {
            font-family: 'Tahoma', Aria<PERSON>, sans-serif;
            margin: 20px;
            background: #f1f1f1;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .success { border-right: 4px solid #28a745; background: #d4edda; }
        .error { border-right: 4px solid #dc3545; background: #f8d7da; }
        .warning { border-right: 4px solid #ffc107; background: #fff3cd; }
        .info { border-right: 4px solid #17a2b8; background: #d1ecf1; }
        
        .menu-list {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .menu-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .menu-item:last-child { border-bottom: none; }
        .menu-item:hover { background: #f8f9fa; }
        
        .menu-title { font-weight: bold; color: #333; }
        .menu-slug { color: #666; font-size: 0.9em; }
        .menu-link {
            background: #007cba;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 0.9em;
        }
        .menu-link:hover {
            background: #005a87;
            color: white;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تست منوهای ادمین پلاگین SETIA</h1>
            <p>بررسی وضعیت منوها و شناسایی تکرارها</p>
        </div>

        <?php
        // دریافت منوهای ادمین
        global $menu, $submenu;
        
        // پیدا کردن منوی اصلی SETIA
        $setia_menu_found = false;
        $setia_menu_position = null;
        $setia_submenus = array();
        
        foreach ($menu as $position => $menu_item) {
            if (isset($menu_item[2]) && $menu_item[2] === 'setia-content-generator') {
                $setia_menu_found = true;
                $setia_menu_position = $position;
                break;
            }
        }
        
        // دریافت زیرمنوها
        if (isset($submenu['setia-content-generator'])) {
            $setia_submenus = $submenu['setia-content-generator'];
        }
        
        // آمار
        $total_submenus = count($setia_submenus);
        $unique_slugs = array_unique(array_column($setia_submenus, 2));
        $duplicate_count = $total_submenus - count($unique_slugs);
        ?>

        <div class="stats">
            <div class="stat-box">
                <div class="stat-number"><?php echo $setia_menu_found ? '✅' : '❌'; ?></div>
                <div class="stat-label">منوی اصلی</div>
            </div>
            <div class="stat-box">
                <div class="stat-number"><?php echo $total_submenus; ?></div>
                <div class="stat-label">تعداد زیرمنوها</div>
            </div>
            <div class="stat-box">
                <div class="stat-number"><?php echo count($unique_slugs); ?></div>
                <div class="stat-label">منوهای یکتا</div>
            </div>
            <div class="stat-box">
                <div class="stat-number"><?php echo $duplicate_count; ?></div>
                <div class="stat-label">منوهای تکراری</div>
            </div>
        </div>

        <!-- وضعیت منوی اصلی -->
        <div class="status-card <?php echo $setia_menu_found ? 'success' : 'error'; ?>">
            <h3>📋 وضعیت منوی اصلی</h3>
            <?php if ($setia_menu_found): ?>
                <p>✅ منوی اصلی "تولید محتوا با هوش مصنوعی" در موقعیت <?php echo $setia_menu_position; ?> یافت شد.</p>
                <p><strong>عنوان:</strong> <?php echo $menu[$setia_menu_position][0]; ?></p>
                <p><strong>Slug:</strong> <?php echo $menu[$setia_menu_position][2]; ?></p>
                <p><strong>آیکون:</strong> <?php echo $menu[$setia_menu_position][6]; ?></p>
            <?php else: ?>
                <p>❌ منوی اصلی پیدا نشد!</p>
            <?php endif; ?>
        </div>

        <!-- وضعیت تکرار -->
        <?php if ($duplicate_count > 0): ?>
            <div class="status-card error">
                <h3>⚠️ هشدار: منوهای تکراری شناسایی شد</h3>
                <p>تعداد <?php echo $duplicate_count; ?> منوی تکراری یافت شد که باید رفع شوند.</p>
            </div>
        <?php else: ?>
            <div class="status-card success">
                <h3>✅ عدم وجود تکرار</h3>
                <p>هیچ منوی تکراری یافت نشد. تمام منوها یکتا هستند.</p>
            </div>
        <?php endif; ?>

        <!-- لیست زیرمنوها -->
        <h3>📂 لیست زیرمنوهای SETIA</h3>
        <div class="menu-list">
            <?php if (!empty($setia_submenus)): ?>
                <?php foreach ($setia_submenus as $index => $submenu_item): ?>
                    <div class="menu-item">
                        <div>
                            <div class="menu-title"><?php echo esc_html($submenu_item[0]); ?></div>
                            <div class="menu-slug">Slug: <?php echo esc_html($submenu_item[2]); ?></div>
                        </div>
                        <a href="<?php echo admin_url('admin.php?page=' . $submenu_item[2]); ?>" 
                           class="menu-link" target="_blank">
                            مشاهده صفحه
                        </a>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="menu-item">
                    <div class="menu-title" style="color: #dc3545;">هیچ زیرمنویی یافت نشد!</div>
                </div>
            <?php endif; ?>
        </div>

        <!-- بررسی تابع‌های صفحات -->
        <h3>🔧 بررسی تابع‌های صفحات</h3>
        <?php
        $page_functions = array(
            'main_page' => 'صفحه اصلی',
            'settings_page' => 'صفحه تنظیمات',
            'history_page' => 'صفحه تاریخچه',
            'schema_settings_page' => 'صفحه تنظیمات اسکیما',
            'scheduler_page' => 'صفحه زمانبندی',
            'cron_settings_page' => 'صفحه تنظیمات کرون سرور',
            'internal_cron_page' => 'صفحه کرون داخلی'
        );
        
        $setia_instance = new SETIA_Content_Generator();
        ?>
        
        <div class="menu-list">
            <?php foreach ($page_functions as $function => $title): ?>
                <div class="menu-item">
                    <div>
                        <div class="menu-title"><?php echo $title; ?></div>
                        <div class="menu-slug">تابع: <?php echo $function; ?>()</div>
                    </div>
                    <div>
                        <?php if (method_exists($setia_instance, $function)): ?>
                            <span style="color: #28a745; font-weight: bold;">✅ موجود</span>
                        <?php else: ?>
                            <span style="color: #dc3545; font-weight: bold;">❌ ناموجود</span>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- نتیجه‌گیری -->
        <div class="status-card info">
            <h3>📊 نتیجه‌گیری</h3>
            <?php if ($setia_menu_found && $duplicate_count === 0): ?>
                <p>✅ <strong>وضعیت عالی:</strong> منوهای پلاگین SETIA به درستی تنظیم شده‌اند و هیچ تکراری وجود ندارد.</p>
            <?php elseif ($setia_menu_found && $duplicate_count > 0): ?>
                <p>⚠️ <strong>نیاز به بهبود:</strong> منوی اصلی موجود است اما <?php echo $duplicate_count; ?> منوی تکراری وجود دارد.</p>
            <?php else: ?>
                <p>❌ <strong>مشکل جدی:</strong> منوی اصلی پیدا نشد. لطفاً پلاگین را بررسی کنید.</p>
            <?php endif; ?>
        </div>

        <div style="text-align: center; margin-top: 20px; color: #666;">
            <p>📅 تاریخ تست: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p>🔄 برای به‌روزرسانی نتایج، صفحه را رفرش کنید</p>
        </div>
    </div>
</body>
</html>
