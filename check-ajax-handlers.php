<?php
/**
 * بررسی دقیق AJAX handlers پلاگین SETIA
 */

// بارگذاری وردپرس
require_once('../../../wp-load.php');

// بررسی دسترسی
if (!current_user_can('manage_options')) {
    wp_die('شما اجازه دسترسی به این صفحه را ندارید.');
}

echo '<html dir="rtl"><head><meta charset="UTF-8">';
echo '<title>بررسی AJAX Handlers</title>';
echo '<style>
    body { font-family: Tahoma, Arial, sans-serif; direction: rtl; padding: 20px; background: #f1f1f1; }
    .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #28a745; }
    .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #dc3545; }
    .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #ffc107; }
    .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #17a2b8; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
    h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { padding: 12px; border: 1px solid #ddd; text-align: right; }
    th { background: #f8f9fa; font-weight: bold; }
    .test-button { background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
    .test-button:hover { background: #005a87; }
    .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; border-right: 3px solid #007cba; }
</style></head><body>';

echo '<div class="container">';
echo '<h1>🔍 بررسی دقیق AJAX Handlers پلاگین SETIA</h1>';

// 1. بررسی ثبت اکشن‌های AJAX در WordPress
echo '<div class="section">';
echo '<h2>1️⃣ بررسی ثبت اکشن‌های AJAX در WordPress</h2>';

global $wp_filter;

$required_ajax_actions = [
    'wp_ajax_setia_save_schedule' => 'ذخیره زمانبندی',
    'wp_ajax_setia_get_schedules' => 'دریافت زمانبندی‌ها',
    'wp_ajax_setia_delete_schedule' => 'حذف زمانبندی',
    'wp_ajax_setia_save_scheduler_settings' => 'ذخیره تنظیمات زمانبندی',
    'wp_ajax_setia_test_scheduler' => 'تست زمانبندی',
    'wp_ajax_setia_run_scheduler_now' => 'اجرای فوری زمانبندی',
    'wp_ajax_setia_clear_scheduler_logs' => 'پاک کردن لاگ‌ها',
    'wp_ajax_setia_get_scheduler_logs' => 'دریافت لاگ‌ها'
];

echo '<table>';
echo '<tr><th>اکشن AJAX</th><th>توضیحات</th><th>وضعیت</th><th>تعداد Callback</th></tr>';

$registered_count = 0;
foreach ($required_ajax_actions as $action => $description) {
    echo '<tr>';
    echo '<td><code>' . $action . '</code></td>';
    echo '<td>' . $description . '</td>';
    
    if (isset($wp_filter[$action])) {
        $callbacks_count = count($wp_filter[$action]->callbacks);
        echo '<td><span style="color: green;">✅ ثبت شده</span></td>';
        echo '<td>' . $callbacks_count . '</td>';
        $registered_count++;
    } else {
        echo '<td><span style="color: red;">❌ ثبت نشده</span></td>';
        echo '<td>0</td>';
    }
    
    echo '</tr>';
}

echo '</table>';

if ($registered_count === count($required_ajax_actions)) {
    echo '<div class="success">🎉 تمام اکشن‌های AJAX مورد نیاز ثبت شده‌اند!</div>';
} else {
    echo '<div class="error">⚠️ ' . (count($required_ajax_actions) - $registered_count) . ' اکشن AJAX ثبت نشده است.</div>';
}

echo '</div>';

// 2. بررسی کلاس‌های مربوطه
echo '<div class="section">';
echo '<h2>2️⃣ بررسی کلاس‌های مربوط به AJAX</h2>';

$classes_to_check = [
    'SETIA_Scheduler' => 'کلاس زمانبندی',
    'SETIA_Ajax_Handlers' => 'کلاس مدیریت AJAX',
    'SETIA_Content_Generator' => 'کلاس اصلی پلاگین'
];

foreach ($classes_to_check as $class => $description) {
    if (class_exists($class)) {
        echo '<div class="success">✅ ' . $description . ' (' . $class . ') موجود است</div>';
        
        // بررسی متدهای کلیدی
        if ($class === 'SETIA_Scheduler') {
            $methods = ['save_schedule', 'delete_schedule', 'get_schedules'];
            foreach ($methods as $method) {
                if (method_exists($class, $method)) {
                    echo '<div class="info">  ✓ متد ' . $method . ' موجود است</div>';
                } else {
                    echo '<div class="error">  ✗ متد ' . $method . ' موجود نیست</div>';
                }
            }
        }
    } else {
        echo '<div class="error">❌ ' . $description . ' (' . $class . ') موجود نیست</div>';
    }
}

echo '</div>';

// 3. تست عملی AJAX
echo '<div class="section">';
echo '<h2>3️⃣ تست عملی AJAX</h2>';

echo '<div class="info">برای تست عملی AJAX، از دکمه‌های زیر استفاده کنید:</div>';

echo '<button class="test-button" onclick="testGetSchedules()">تست دریافت زمانبندی‌ها</button>';
echo '<button class="test-button" onclick="testSaveSchedule()">تست ذخیره زمانبندی</button>';
echo '<button class="test-button" onclick="testGetLogs()">تست دریافت لاگ‌ها</button>';

echo '<div id="ajax-results" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; min-height: 100px;"></div>';

echo '</div>';

// 4. بررسی nonce
echo '<div class="section">';
echo '<h2>4️⃣ بررسی سیستم امنیتی (Nonce)</h2>';

$nonce = wp_create_nonce('setia-nonce');
echo '<div class="success">✅ Nonce تولید شد: <code>' . substr($nonce, 0, 20) . '...</code></div>';

echo '<div class="code">';
echo 'نمونه کد JavaScript برای استفاده از nonce:<br>';
echo 'var nonce = \'' . $nonce . '\';<br>';
echo 'data: { action: \'setia_get_schedules\', nonce: nonce }';
echo '</div>';

echo '</div>';

// 5. بررسی فایل‌های مربوطه
echo '<div class="section">';
echo '<h2>5️⃣ بررسی فایل‌های مربوط به AJAX</h2>';

$files_to_check = [
    'includes/scheduler.php' => 'فایل کلاس زمانبندی',
    'ajax-handlers.php' => 'فایل مدیریت AJAX',
    'templates/scheduler-page.php' => 'فایل صفحه زمانبندی'
];

foreach ($files_to_check as $file => $description) {
    $full_path = plugin_dir_path(__FILE__) . $file;
    if (file_exists($full_path)) {
        $file_size = filesize($full_path);
        echo '<div class="success">✅ ' . $description . ' (' . $file . ') - ' . number_format($file_size) . ' بایت</div>';
    } else {
        echo '<div class="error">❌ ' . $description . ' (' . $file . ') موجود نیست</div>';
    }
}

echo '</div>';

// 6. خلاصه و توصیه‌ها
echo '<div class="section">';
echo '<h2>📋 خلاصه و توصیه‌ها</h2>';

$issues = [];
if ($registered_count < count($required_ajax_actions)) {
    $issues[] = 'برخی اکشن‌های AJAX ثبت نشده‌اند';
}
if (!class_exists('SETIA_Scheduler')) {
    $issues[] = 'کلاس زمانبندی بارگذاری نشده';
}

if (empty($issues)) {
    echo '<div class="success">🎉 تمام AJAX handlers به درستی تنظیم شده‌اند!</div>';
    echo '<div class="info">💡 می‌توانید از دکمه‌های تست بالا برای آزمایش عملکرد استفاده کنید.</div>';
} else {
    echo '<div class="error">⚠️ مشکلات شناسایی شده:</div>';
    echo '<ul>';
    foreach ($issues as $issue) {
        echo '<li>' . $issue . '</li>';
    }
    echo '</ul>';
}

echo '</div>';

echo '</div>';

// JavaScript برای تست‌های AJAX
echo '<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>';
echo '<script>
const ajaxUrl = "/WP/wp-admin/admin-ajax.php";
const nonce = "' . $nonce . '";

function addAjaxResult(message, type = "info") {
    const results = document.getElementById("ajax-results");
    const div = document.createElement("div");
    div.className = type;
    div.innerHTML = new Date().toLocaleTimeString("fa-IR") + " - " + message;
    results.appendChild(div);
    results.scrollTop = results.scrollHeight;
}

function testGetSchedules() {
    addAjaxResult("🔄 تست دریافت زمانبندی‌ها...", "info");
    
    $.ajax({
        url: ajaxUrl,
        type: "POST",
        data: {
            action: "setia_get_schedules",
            nonce: nonce
        },
        success: function(response) {
            if (response.success) {
                const count = response.data.schedules ? response.data.schedules.length : 0;
                addAjaxResult("✅ موفق: " + count + " زمانبندی یافت شد", "success");
            } else {
                addAjaxResult("❌ خطا: " + (response.data ? response.data.message : "نامشخص"), "error");
            }
        },
        error: function(xhr, status, error) {
            addAjaxResult("❌ خطا در اتصال: " + error, "error");
        }
    });
}

function testSaveSchedule() {
    addAjaxResult("🔄 تست ذخیره زمانبندی...", "info");
    
    $.ajax({
        url: ajaxUrl,
        type: "POST",
        data: {
            action: "setia_save_schedule",
            nonce: nonce,
            title: "تست AJAX",
            topic: "موضوع تست",
            keywords: "تست, ajax",
            frequency: "daily",
            status: "active",
            schedule_id: 0
        },
        success: function(response) {
            if (response.success) {
                addAjaxResult("✅ موفق: زمانبندی ذخیره شد", "success");
            } else {
                addAjaxResult("❌ خطا: " + (response.data ? response.data.message : "نامشخص"), "error");
            }
        },
        error: function(xhr, status, error) {
            addAjaxResult("❌ خطا در اتصال: " + error, "error");
        }
    });
}

function testGetLogs() {
    addAjaxResult("🔄 تست دریافت لاگ‌ها...", "info");
    
    $.ajax({
        url: ajaxUrl,
        type: "POST",
        data: {
            action: "setia_get_scheduler_logs",
            nonce: nonce
        },
        success: function(response) {
            if (response.success) {
                const count = response.data.logs ? response.data.logs.length : 0;
                addAjaxResult("✅ موفق: " + count + " لاگ یافت شد", "success");
            } else {
                addAjaxResult("❌ خطا: " + (response.data ? response.data.message : "نامشخص"), "error");
            }
        },
        error: function(xhr, status, error) {
            addAjaxResult("❌ خطا در اتصال: " + error, "error");
        }
    });
}

// تست اولیه
$(document).ready(function() {
    addAjaxResult("🚀 سیستم تست AJAX آماده است", "success");
});
</script>';

echo '</body></html>';
?>
