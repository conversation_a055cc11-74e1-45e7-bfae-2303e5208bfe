/**
 * SETIA Advanced History Page Styles
 * Modern, Professional, RTL-Compatible Design
 * 
 * @package SETIA
 * @version 2.0
 */

/* ==========================================================================
   Base Styles & Variables
   ========================================================================== */

:root {
    --setia-primary: #2271b1;
    --setia-primary-hover: #135e96;
    --setia-success: #00a32a;
    --setia-success-hover: #008a20;
    --setia-danger: #d63638;
    --setia-danger-hover: #b32d2e;
    --setia-warning: #dba617;
    --setia-warning-hover: #c69214;
    --setia-info: #72aee6;
    --setia-light: #f6f7f7;
    --setia-dark: #1d2327;
    --setia-border: #c3c4c7;
    --setia-border-light: #e0e0e0;
    --setia-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --setia-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
    --setia-radius: 8px;
    --setia-transition: all 0.3s ease;
}

/* ==========================================================================
   RTL Support
   ========================================================================== */

.setia-history-advanced {
    direction: rtl;
    text-align: right;
    font-family: 'Vazir', 'Tahoma', sans-serif;
}

.setia-history-advanced * {
    box-sizing: border-box;
}

/* ==========================================================================
   Page Header
   ========================================================================== */

.setia-page-header {
    background: linear-gradient(135deg, var(--setia-primary) 0%, var(--setia-primary-hover) 100%);
    color: white;
    padding: 2rem;
    margin: 0 -20px 2rem -20px;
    border-radius: 0 0 var(--setia-radius) var(--setia-radius);
    box-shadow: var(--setia-shadow);
}

.setia-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    flex-wrap: wrap;
    gap: 1rem;
}

.setia-header-title {
    flex: 1;
}

.setia-main-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.setia-icon {
    font-size: 1.5rem;
}

.setia-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 400;
}

.setia-header-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* ==========================================================================
   Statistics Dashboard
   ========================================================================== */

.setia-stats-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.setia-stat-card {
    background: white;
    border: 1px solid var(--setia-border-light);
    border-radius: var(--setia-radius);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--setia-transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.setia-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--setia-primary);
    transition: var(--setia-transition);
}

.setia-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--setia-shadow-hover);
}

.setia-stat-card:hover::before {
    width: 8px;
}

.setia-stat-card[data-stat="published"]::before {
    background: var(--setia-success);
}

.setia-stat-card[data-stat="drafts"]::before {
    background: var(--setia-warning);
}

.setia-stat-card[data-stat="products"]::before {
    background: var(--setia-info);
}

.setia-stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.setia-stat-info {
    flex: 1;
}

.setia-stat-title {
    font-size: 0.875rem;
    color: #666;
    margin: 0 0 0.25rem 0;
    font-weight: 500;
}

.setia-stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--setia-dark);
    display: block;
    line-height: 1;
}

.setia-stat-change {
    font-size: 0.75rem;
    color: #888;
    margin-top: 0.25rem;
    display: block;
}

/* ==========================================================================
   Filters Panel
   ========================================================================== */

.setia-filters-panel {
    background: white;
    border: 1px solid var(--setia-border-light);
    border-radius: var(--setia-radius);
    margin-bottom: 2rem;
    overflow: hidden;
    box-shadow: var(--setia-shadow);
}

.setia-panel-header {
    background: var(--setia-light);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--setia-border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.setia-panel-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--setia-dark);
}

.setia-filters-content {
    padding: 1.5rem;
    transition: var(--setia-transition);
}

.setia-filters-content.hidden {
    display: none;
}

.setia-filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.setia-filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.setia-filter-group label {
    font-weight: 500;
    color: var(--setia-dark);
    font-size: 0.875rem;
}

.setia-filters-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
    flex-wrap: wrap;
}

/* ==========================================================================
   Content Panel
   ========================================================================== */

.setia-content-panel {
    background: white;
    border: 1px solid var(--setia-border-light);
    border-radius: var(--setia-radius);
    box-shadow: var(--setia-shadow);
    overflow: hidden;
}

.setia-panel-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.setia-panel-title h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--setia-dark);
}

.setia-results-count {
    background: var(--setia-light);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    color: #666;
    font-weight: 500;
}

.setia-panel-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.setia-bulk-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.setia-view-options {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* ==========================================================================
   Table Styles
   ========================================================================== */

.setia-table-container {
    overflow-x: auto;
}

.setia-content-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.setia-content-table th,
.setia-content-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--setia-border-light);
    vertical-align: middle;
}

.setia-content-table th {
    background: var(--setia-light);
    font-weight: 600;
    color: var(--setia-dark);
    position: sticky;
    top: 0;
    z-index: 10;
}

.setia-content-table tbody tr {
    transition: var(--setia-transition);
}

.setia-content-table tbody tr:hover {
    background: #f8f9fa;
}

.setia-content-table tbody tr.selected {
    background: #e3f2fd;
}

.setia-checkbox-col {
    width: 40px;
    text-align: center;
}

.setia-actions-col {
    width: 200px;
    text-align: center;
}

.setia-sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.setia-sortable:hover {
    background: #e8e8e8;
}

.setia-sortable .dashicons {
    opacity: 0.5;
    margin-right: 0.25rem;
    transition: var(--setia-transition);
}

.setia-sortable.sorted .dashicons {
    opacity: 1;
    color: var(--setia-primary);
}

/* ==========================================================================
   Loading & Empty States
   ========================================================================== */

.setia-loading-state {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.setia-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--setia-border-light);
    border-top: 4px solid var(--setia-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.setia-empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.setia-empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.setia-empty-state h3 {
    margin: 0 0 0.5rem 0;
    color: var(--setia-dark);
}

.setia-empty-state p {
    margin: 0 0 1.5rem 0;
    color: #888;
}

/* ==========================================================================
   Pagination
   ========================================================================== */

.setia-pagination {
    padding: 1.5rem;
    border-top: 1px solid var(--setia-border-light);
    background: var(--setia-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.setia-pagination-info {
    color: #666;
    font-size: 0.875rem;
}

.setia-pagination-controls {
    display: flex;
    gap: 0.25rem;
}

.setia-pagination-controls button {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--setia-border);
    background: white;
    color: var(--setia-dark);
    border-radius: 4px;
    cursor: pointer;
    transition: var(--setia-transition);
    font-size: 0.875rem;
}

.setia-pagination-controls button:hover:not(:disabled) {
    background: var(--setia-primary);
    color: white;
    border-color: var(--setia-primary);
}

.setia-pagination-controls button.active {
    background: var(--setia-primary);
    color: white;
    border-color: var(--setia-primary);
}

.setia-pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ==========================================================================
   Form Elements
   ========================================================================== */

.setia-input,
.setia-select,
.setia-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--setia-border);
    border-radius: 4px;
    font-size: 0.875rem;
    transition: var(--setia-transition);
    background: white;
    color: var(--setia-dark);
    direction: rtl;
    text-align: right;
}

.setia-input:focus,
.setia-select:focus,
.setia-textarea:focus {
    outline: none;
    border-color: var(--setia-primary);
    box-shadow: 0 0 0 2px rgba(34, 113, 177, 0.1);
}

.setia-textarea {
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
}

.setia-checkbox {
    width: 18px;
    height: 18px;
    accent-color: var(--setia-primary);
}

/* ==========================================================================
   Buttons
   ========================================================================== */

.setia-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--setia-transition);
    text-decoration: none;
    white-space: nowrap;
    direction: rtl;
}

.setia-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.setia-btn-primary {
    background: var(--setia-primary);
    color: white;
}

.setia-btn-primary:hover:not(:disabled) {
    background: var(--setia-primary-hover);
}

.setia-btn-success {
    background: var(--setia-success);
    color: white;
}

.setia-btn-success:hover:not(:disabled) {
    background: var(--setia-success-hover);
}

.setia-btn-danger {
    background: var(--setia-danger);
    color: white;
}

.setia-btn-danger:hover:not(:disabled) {
    background: var(--setia-danger-hover);
}

.setia-btn-warning {
    background: var(--setia-warning);
    color: white;
}

.setia-btn-warning:hover:not(:disabled) {
    background: var(--setia-warning-hover);
}

.setia-btn-outline {
    background: transparent;
    color: var(--setia-primary);
    border: 1px solid var(--setia-primary);
}

.setia-btn-outline:hover:not(:disabled) {
    background: var(--setia-primary);
    color: white;
}

.setia-btn-ghost {
    background: transparent;
    color: var(--setia-dark);
    border: 1px solid var(--setia-border);
}

.setia-btn-ghost:hover:not(:disabled) {
    background: var(--setia-light);
}

.setia-btn-secondary {
    background: #6c757d;
    color: white;
}

.setia-btn-secondary:hover:not(:disabled) {
    background: #5a6268;
}

.setia-btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.setia-btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* ==========================================================================
   Modals
   ========================================================================== */

.setia-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.setia-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.setia-modal-container {
    position: relative;
    background: white;
    border-radius: var(--setia-radius);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: modalSlideIn 0.3s ease-out;
}

.setia-modal-large {
    max-width: 900px;
}

.setia-modal-small {
    max-width: 400px;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.setia-modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--setia-border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--setia-light);
}

.setia-modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--setia-dark);
}

.setia-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0.25rem;
    border-radius: 4px;
    transition: var(--setia-transition);
}

.setia-modal-close:hover {
    background: #e0e0e0;
    color: var(--setia-dark);
}

.setia-modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
}

.setia-modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--setia-border-light);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    background: var(--setia-light);
}

/* ==========================================================================
   Form Layout
   ========================================================================== */

.setia-form-group {
    margin-bottom: 1.5rem;
}

.setia-form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--setia-dark);
}

.setia-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* ==========================================================================
   Status Badges
   ========================================================================== */

.setia-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
}

.setia-status-published {
    background: #d4edda;
    color: #155724;
}

.setia-status-draft {
    background: #fff3cd;
    color: #856404;
}

.setia-status-pending {
    background: #cce5ff;
    color: #004085;
}

.setia-status-failed {
    background: #f8d7da;
    color: #721c24;
}

/* ==========================================================================
   Action Buttons
   ========================================================================== */

.setia-action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.setia-action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: var(--setia-transition);
    font-size: 0.875rem;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.setia-action-btn:hover {
    transform: translateY(-1px);
}

.setia-action-view {
    background: var(--setia-info);
    color: white;
}

.setia-action-edit {
    background: var(--setia-warning);
    color: white;
}

.setia-action-publish {
    background: var(--setia-success);
    color: white;
}

.setia-action-delete {
    background: var(--setia-danger);
    color: white;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .setia-page-header {
        padding: 1.5rem;
        margin: 0 -10px 1.5rem -10px;
    }

    .setia-header-content {
        flex-direction: column;
        text-align: center;
    }

    .setia-main-title {
        font-size: 1.5rem;
    }

    .setia-stats-dashboard {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .setia-filters-grid {
        grid-template-columns: 1fr;
    }

    .setia-panel-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .setia-panel-actions {
        flex-direction: column;
        width: 100%;
    }

    .setia-bulk-actions,
    .setia-view-options {
        width: 100%;
        justify-content: center;
    }

    .setia-content-table {
        font-size: 0.75rem;
    }

    .setia-content-table th,
    .setia-content-table td {
        padding: 0.5rem;
    }

    .setia-pagination {
        flex-direction: column;
        text-align: center;
    }

    .setia-modal {
        padding: 1rem;
    }

    .setia-modal-container {
        max-height: 95vh;
    }

    .setia-form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .setia-action-buttons {
        flex-direction: column;
    }

    .setia-filters-actions {
        flex-direction: column;
    }

    .setia-modal-footer {
        flex-direction: column;
    }
}

/* ==========================================================================
   Notifications
   ========================================================================== */

.setia-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 10000;
    background: white;
    border: 1px solid var(--setia-border);
    border-radius: var(--setia-radius);
    padding: 1rem 1.5rem;
    box-shadow: var(--setia-shadow-hover);
    display: flex;
    align-items: center;
    gap: 1rem;
    max-width: 400px;
    animation: slideInRight 0.3s ease-out;
    direction: rtl;
}

.setia-notification-success {
    border-left: 4px solid var(--setia-success);
    background: #f0f9ff;
}

.setia-notification-error {
    border-left: 4px solid var(--setia-danger);
    background: #fef2f2;
}

.setia-notification-warning {
    border-left: 4px solid var(--setia-warning);
    background: #fffbeb;
}

.setia-notification-info {
    border-left: 4px solid var(--setia-info);
    background: #f0f9ff;
}

.setia-notification-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    margin-left: auto;
    transition: var(--setia-transition);
}

.setia-notification-close:hover {
    color: var(--setia-dark);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ==========================================================================
   Content Preview Styles
   ========================================================================== */

.setia-content-preview {
    direction: rtl;
    text-align: right;
}

.setia-content-meta {
    background: var(--setia-light);
    padding: 1rem;
    border-radius: var(--setia-radius);
    margin-bottom: 1.5rem;
}

.setia-content-meta p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
}

.setia-content-body h3 {
    margin: 0 0 1rem 0;
    color: var(--setia-dark);
    font-size: 1.125rem;
}

.setia-content-text {
    background: white;
    border: 1px solid var(--setia-border-light);
    border-radius: var(--setia-radius);
    padding: 1.5rem;
    max-height: 400px;
    overflow-y: auto;
    line-height: 1.8;
    font-size: 0.875rem;
}

/* ==========================================================================
   Loading Overlay
   ========================================================================== */

.setia-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    z-index: 9998;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
}

.setia-loading-overlay .setia-spinner {
    width: 60px;
    height: 60px;
    border-width: 6px;
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.setia-text-center {
    text-align: center;
}

.setia-text-right {
    text-align: right;
}

.setia-text-left {
    text-align: left;
}

.setia-mb-0 {
    margin-bottom: 0;
}

.setia-mb-1 {
    margin-bottom: 0.5rem;
}

.setia-mb-2 {
    margin-bottom: 1rem;
}

.setia-mb-3 {
    margin-bottom: 1.5rem;
}

.setia-mt-0 {
    margin-top: 0;
}

.setia-mt-1 {
    margin-top: 0.5rem;
}

.setia-mt-2 {
    margin-top: 1rem;
}

.setia-mt-3 {
    margin-top: 1.5rem;
}

.setia-hidden {
    display: none !important;
}

.setia-visible {
    display: block !important;
}

.setia-flex {
    display: flex;
}

.setia-flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.setia-gap-1 {
    gap: 0.5rem;
}

.setia-gap-2 {
    gap: 1rem;
}

.setia-gap-3 {
    gap: 1.5rem;
}

.setia-w-full {
    width: 100%;
}

.setia-h-full {
    height: 100%;
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .setia-page-header,
    .setia-filters-panel,
    .setia-panel-actions,
    .setia-action-buttons,
    .setia-pagination {
        display: none !important;
    }

    .setia-content-table {
        font-size: 0.75rem;
    }

    .setia-content-table th,
    .setia-content-table td {
        padding: 0.25rem;
    }
}
