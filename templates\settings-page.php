<?php
/**
 * SETIA Content Generator - Modern Settings Page
 * Redesigned with Windows 11 Flat Design principles
 *
 * @package SETIA
 * @version 2.0
 */

// امنیت: جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

// دریافت تنظیمات ذخیره شده
$settings = get_option('setia_settings', array());
$gemini_api_key = isset($settings['gemini_api_key']) ? $settings['gemini_api_key'] : '';
$gemma_api_key = isset($settings['gemma_api_key']) ? $settings['gemma_api_key'] : '';
$imagine_art_api_key = isset($settings['imagine_art_api_key']) ? $settings['imagine_art_api_key'] : '';
$default_tone = isset($settings['default_tone']) ? $settings['default_tone'] : 'عادی';
$default_length = isset($settings['default_length']) ? $settings['default_length'] : 'متوسط';
$enable_seo = isset($settings['enable_seo']) ? $settings['enable_seo'] : 'yes';
$enable_image_generation = isset($settings['enable_image_generation']) ? $settings['enable_image_generation'] : 'yes';
$default_image_style = isset($settings['default_image_style']) ? $settings['default_image_style'] : 'realistic';
$default_aspect_ratio = isset($settings['default_aspect_ratio']) ? $settings['default_aspect_ratio'] : '16:9';
$internal_cron_interval = isset($settings['internal_cron_interval']) ? $settings['internal_cron_interval'] : 15;

// مدیریت ذخیره تنظیمات
$save_success = false;
$save_message = '';

if (isset($_POST['submit_settings']) && check_admin_referer('setia_settings')) {
    try {
        // دریافت و اعتبارسنجی داده‌ها
        $new_settings = array();
        $new_settings['gemini_api_key'] = sanitize_text_field($_POST['gemini_api_key']);
        $new_settings['gemma_api_key'] = sanitize_text_field($_POST['gemma_api_key']);
        $new_settings['imagine_art_api_key'] = sanitize_text_field($_POST['imagine_art_api_key']);
        $new_settings['default_tone'] = sanitize_text_field($_POST['default_tone']);
        $new_settings['default_length'] = sanitize_text_field($_POST['default_length']);
        $new_settings['enable_seo'] = sanitize_text_field($_POST['enable_seo']);
        $new_settings['enable_image_generation'] = sanitize_text_field($_POST['enable_image_generation']);
        $new_settings['default_image_style'] = sanitize_text_field($_POST['default_image_style']);
        $new_settings['default_aspect_ratio'] = sanitize_text_field($_POST['default_aspect_ratio']);
        $new_settings['internal_cron_interval'] = max(1, absint($_POST['internal_cron_interval']));

        // بروزرسانی تنظیمات
        $update_result = update_option('setia_settings', $new_settings);

        if ($update_result !== false) {
            // بروزرسانی گزینه‌های کرون داخلی
            update_option('setia_admin_cron_interval', $new_settings['internal_cron_interval']);
            update_option('setia_internal_cron_interval', $new_settings['internal_cron_interval']);

            // بروزرسانی متغیرها
            $settings = $new_settings;
            $gemini_api_key = $settings['gemini_api_key'];
            $gemma_api_key = $settings['gemma_api_key'];
            $imagine_art_api_key = $settings['imagine_art_api_key'];
            $default_tone = $settings['default_tone'];
            $default_length = $settings['default_length'];
            $enable_seo = $settings['enable_seo'];
            $enable_image_generation = $settings['enable_image_generation'];
            $default_image_style = $settings['default_image_style'];
            $default_aspect_ratio = $settings['default_aspect_ratio'];
            $internal_cron_interval = $settings['internal_cron_interval'];

            $save_success = true;
            $save_message = 'تنظیمات با موفقیت ذخیره شدند';
        } else {
            $save_message = 'خطا در ذخیره تنظیمات';
        }
    } catch (Exception $e) {
        $save_message = 'خطا در پردازش تنظیمات: ' . $e->getMessage();
    }
}

// مدیریت پاک‌سازی کش
$cache_cleared = false;
$cache_message = '';

if (isset($_POST['clear_setia_cache']) && check_admin_referer('setia_clear_cache')) {
    try {
        // اجرای عملیات پاک‌سازی کش
        if (function_exists('setia_clear_cache')) {
            setia_clear_cache();
        } else {
            // روش جایگزین: به‌روزرسانی گزینه برای اجبار به پاکسازی کش
            update_option('setia_css_version', time());
            update_option('setia_js_version', time());
        }

        $cache_cleared = true;
        $cache_message = 'کش افزونه با موفقیت پاک‌سازی شد';
    } catch (Exception $e) {
        $cache_message = 'خطا در پاک‌سازی کش: ' . $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<style type="text/css">
/* SETIA Modern Settings - Critical CSS */
:root {
    --setia-primary: #0078d4;
    --setia-primary-hover: #106ebe;
    --setia-success: #107c10;
    --setia-warning: #ff8c00;
    --setia-error: #d13438;
    --setia-gray-50: #fafafa;
    --setia-gray-100: #f5f5f5;
    --setia-gray-200: #e5e5e5;
    --setia-gray-300: #d4d4d4;
    --setia-gray-600: #6b7280;
    --setia-gray-900: #111827;
    --setia-white: #ffffff;
    --setia-radius: 6px;
    --setia-radius-lg: 8px;
    --setia-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --setia-shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --setia-transition: all 0.2s ease-in-out;
}

/* فونت‌های فارسی */
@font-face {
    font-family: 'IRANSans';
    font-style: normal;
    font-weight: 400;
    src: url('<?php echo plugin_dir_url(__FILE__); ?>../assets/fonts/IRANSansWeb.woff2') format('woff2'),
         url('<?php echo plugin_dir_url(__FILE__); ?>../assets/fonts/IRANSansWeb.woff') format('woff');
    font-display: swap;
}

@font-face {
    font-family: 'IRANSans';
    font-style: normal;
    font-weight: 700;
    src: url('<?php echo plugin_dir_url(__FILE__); ?>../assets/fonts/IRANSansWeb_Bold.woff2') format('woff2'),
         url('<?php echo plugin_dir_url(__FILE__); ?>../assets/fonts/IRANSansWeb_Bold.woff') format('woff');
    font-display: swap;
}

/* Reset و Base Styles */
.wrap.setia-settings * {
    box-sizing: border-box;
}

.wrap.setia-settings {
    font-family: "IRANSans", "Segoe UI", Tahoma, sans-serif;
    direction: rtl;
    background: var(--setia-gray-50);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}
</style>

<!-- نمایش پیام‌های سیستم -->
<?php if ($save_success): ?>
<div class="setia-notification setia-notification-success" id="save-notification">
    <div class="setia-notification-content">
        <svg class="setia-notification-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span><?php echo esc_html($save_message); ?></span>
    </div>
</div>
<?php elseif (!empty($save_message)): ?>
<div class="setia-notification setia-notification-error" id="save-notification">
    <div class="setia-notification-content">
        <svg class="setia-notification-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span><?php echo esc_html($save_message); ?></span>
    </div>
</div>
<?php endif; ?>

<?php if ($cache_cleared): ?>
<div class="setia-notification setia-notification-success" id="cache-notification">
    <div class="setia-notification-content">
        <svg class="setia-notification-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span><?php echo esc_html($cache_message); ?></span>
    </div>
</div>
<?php elseif (!empty($cache_message)): ?>
<div class="setia-notification setia-notification-error" id="cache-notification">
    <div class="setia-notification-content">
        <svg class="setia-notification-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span><?php echo esc_html($cache_message); ?></span>
    </div>
</div>
<?php endif; ?>

<div class="wrap setia-settings">
    <!-- هدر مدرن -->
    <div class="setia-header">
        <div class="setia-header-content">
            <div class="setia-header-main">
                <div class="setia-header-icon">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="setia-header-text">
                    <h1 class="setia-header-title">تنظیمات SETIA</h1>
                    <p class="setia-header-subtitle">مولد محتوای هوشمند با قدرت هوش مصنوعی</p>
                </div>
            </div>
            <div class="setia-header-actions">
                <div class="setia-status-indicators">
                    <div class="setia-status-item" id="gemini-status">
                        <span class="setia-status-dot"></span>
                        <span class="setia-status-text">Gemini API</span>
                    </div>
                    <div class="setia-status-item" id="imagine-status">
                        <span class="setia-status-dot"></span>
                        <span class="setia-status-text">Imagine Art</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- منوی تب‌ها -->
    <div class="setia-tabs">
        <div class="setia-tabs-nav">
            <button type="button" class="setia-tab-button active" data-tab="api">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>تنظیمات API</span>
            </button>
            <button type="button" class="setia-tab-button" data-tab="content">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15M9 5C9 6.10457 9.89543 7 11 7H13C14.1046 7 15 6.10457 15 5M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5M12 12H15M12 16H15M9 12H9.01M9 16H9.01" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>تولید محتوا</span>
            </button>
            <button type="button" class="setia-tab-button" data-tab="image">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M4 16L8.586 11.414C9.367 10.633 10.633 10.633 11.414 11.414L16 16M14 14L15.586 12.414C16.367 11.633 17.633 11.633 18.414 12.414L20 14M14 8H14.01M6 20H18C19.1046 20 20 19.1046 20 18V6C20 4.89543 19.1046 4 18 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20Z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>تصاویر</span>
            </button>
            <button type="button" class="setia-tab-button" data-tab="system">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M10.325 4.317C10.751 2.561 13.249 2.561 13.675 4.317C13.7389 4.5808 13.8642 4.82578 14.0407 5.032C14.2172 5.23822 14.4399 5.39985 14.6907 5.50375C14.9414 5.60764 15.2132 5.65085 15.4838 5.62987C15.7544 5.60889 16.0162 5.5243 16.248 5.383C17.791 4.443 19.558 6.209 18.618 7.753C18.4769 7.98466 18.3924 8.24634 18.3715 8.51677C18.3506 8.78721 18.3938 9.05877 18.4975 9.30938C18.6013 9.55999 18.7627 9.78258 18.9687 9.95905C19.1747 10.1355 19.4194 10.2609 19.683 10.325C21.439 10.751 21.439 13.249 19.683 13.675C19.4192 13.7389 19.1742 13.8642 18.968 14.0407C18.7618 14.2172 18.6001 14.4399 18.4963 14.6907C18.3924 14.9414 18.3491 15.2132 18.3701 15.4838C18.3911 15.7544 18.4757 16.0162 18.617 16.248C19.557 17.791 17.791 19.558 16.247 18.618C16.0153 18.4769 15.7537 18.3924 15.4832 18.3715C15.2128 18.3506 14.9412 18.3938 14.6906 18.4975C14.44 18.6013 14.2174 18.7627 14.0409 18.9687C13.8645 19.1747 13.7391 19.4194 13.675 19.683C13.249 21.439 10.751 21.439 10.325 19.683C10.2611 19.4192 10.1358 19.1742 9.95929 18.968C9.7828 18.7618 9.56011 18.6001 9.30935 18.4963C9.05859 18.3924 8.78683 18.3491 8.51621 18.3701C8.24559 18.3911 7.98375 18.4757 7.752 18.617C6.209 19.557 4.442 17.791 5.382 16.247C5.5231 16.0153 5.60755 15.7537 5.62848 15.4832C5.64942 15.2128 5.60624 14.9412 5.50247 14.6906C5.3987 14.44 5.23726 14.2174 5.03127 14.0409C4.82529 13.8645 4.58056 13.7391 4.317 13.675C2.561 13.249 2.561 10.751 4.317 10.325C4.5808 10.2611 4.82578 10.1358 5.032 9.95929C5.23822 9.7828 5.39985 9.56011 5.50375 9.30935C5.60764 9.05859 5.65085 8.78683 5.62987 8.51621C5.60889 8.24559 5.5243 7.98375 5.383 7.752C4.443 6.209 6.209 4.442 7.753 5.382C7.98466 5.5231 8.24634 5.60755 8.51677 5.62848C8.78721 5.64942 9.05877 5.60624 9.30938 5.50247C9.55999 5.3987 9.78258 5.23726 9.95905 5.03127C10.1355 4.82529 10.2609 4.58056 10.325 4.317Z" stroke="currentColor" stroke-width="2"/>
                    <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>سیستم</span>
            </button>
        </div>
    </div>

    <!-- محتوای فرم -->
    <div class="setia-container">
        <form method="post" action="" class="setia-form">
            <?php wp_nonce_field('setia_settings'); ?>

            <!-- تب تنظیمات API -->
            <div class="setia-tab-content active" id="tab-api">
                <div class="setia-card">
                    <div class="setia-card-header">
                        <div class="setia-card-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="setia-card-title">
                            <h2>تنظیمات API</h2>
                            <p>کلیدهای دسترسی سرویس‌های هوش مصنوعی را وارد کنید</p>
                        </div>
                    </div>
                    <div class="setia-card-content">
                        <!-- فیلد Gemini API -->
                        <div class="setia-field-group">
                            <label for="gemini_api_key" class="setia-label">
                                <div class="setia-label-content">
                                    <div class="setia-label-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M9.663 17H7.5C6.11929 17 5 15.8807 5 14.5C5 13.1193 6.11929 12 7.5 12C7.5 10.6193 8.61929 9.5 10 9.5C11.3807 9.5 12.5 10.6193 12.5 12H14.337C15.8807 12 17.163 13.2823 17.163 14.826C17.163 16.3697 15.8807 17.652 14.337 17.652H9.663V17Z" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="setia-label-text">
                                        <span class="setia-label-title">کلید API Google Gemini</span>
                                        <span class="setia-label-required">ضروری</span>
                                    </div>
                                </div>
                            </label>
                            <div class="setia-input-container">
                                <input
                                    type="text"
                                    id="gemini_api_key"
                                    name="gemini_api_key"
                                    placeholder="AIzaSy..."
                                    value="<?php echo esc_attr($gemini_api_key); ?>"
                                    class="setia-input"
                                    required
                                    data-api-type="gemini"
                                >
                                <div class="setia-input-status" id="gemini-status-indicator">
                                    <svg class="setia-status-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="setia-field-help">
                                <button type="button" class="setia-help-toggle" data-target="gemini-help">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <path d="M8.228 9C8.43686 8.47471 8.81005 8.03121 9.28844 7.73463C9.76683 7.43805 10.3249 7.30154 10.8851 7.34587C11.4452 7.3902 11.9757 7.61304 12.4007 7.97706C12.8256 8.34108 13.1228 8.82339 13.25 9.36C13.25 12 10.75 13.25 10.75 13.25M12 17.25H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    راهنمای دریافت کلید
                                </button>
                                <div class="setia-help-content" id="gemini-help">
                                    <div class="setia-help-steps">
                                        <h4>مراحل دریافت کلید API:</h4>
                                        <ol>
                                            <li>به <a href="https://makersuite.google.com/app/apikey" target="_blank" rel="noopener">Google AI Studio</a> وارد شوید</li>
                                            <li>روی دکمه «Create API key» کلیک کنید</li>
                                            <li>پروژه مورد نظر را انتخاب یا ایجاد کنید</li>
                                            <li>کلید تولید شده را کپی کرده و در فیلد بالا قرار دهید</li>
                                        </ol>
                                        <div class="setia-help-note">
                                            <strong>نکته:</strong> این کلید برای تولید محتوای متنی و تصویری استفاده می‌شود
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- فیلد Imagine Art API -->
                        <div class="setia-field-group">
                            <label for="imagine_art_api_key" class="setia-label">
                                <div class="setia-label-content">
                                    <div class="setia-label-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M4 16L8.586 11.414C9.367 10.633 10.633 10.633 11.414 11.414L16 16M14 14L15.586 12.414C16.367 11.633 17.633 11.633 18.414 12.414L20 14M14 8H14.01M6 20H18C19.1046 20 20 19.1046 20 18V6C20 4.89543 19.1046 4 18 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20Z" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="setia-label-text">
                                        <span class="setia-label-title">کلید API Imagine Art</span>
                                        <span class="setia-label-optional">اختیاری</span>
                                    </div>
                                </div>
                            </label>
                            <div class="setia-input-container">
                                <input
                                    type="text"
                                    id="imagine_art_api_key"
                                    name="imagine_art_api_key"
                                    placeholder="sk-live-..."
                                    value="<?php echo esc_attr($imagine_art_api_key); ?>"
                                    class="setia-input"
                                    data-api-type="imagine"
                                >
                                <div class="setia-input-status" id="imagine-status-indicator">
                                    <svg class="setia-status-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="setia-field-help">
                                <button type="button" class="setia-help-toggle" data-target="imagine-help">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <path d="M8.228 9C8.43686 8.47471 8.81005 8.03121 9.28844 7.73463C9.76683 7.43805 10.3249 7.30154 10.8851 7.34587C11.4452 7.3902 11.9757 7.61304 12.4007 7.97706C12.8256 8.34108 13.1228 8.82339 13.25 9.36C13.25 12 10.75 13.25 10.75 13.25M12 17.25H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    راهنمای دریافت کلید
                                </button>
                                <div class="setia-help-content" id="imagine-help">
                                    <div class="setia-help-steps">
                                        <h4>مراحل دریافت کلید API:</h4>
                                        <ol>
                                            <li>به <a href="https://vyro.ai/" target="_blank" rel="noopener">سایت Vyro AI</a> وارد شوید</li>
                                            <li>ثبت‌نام کرده یا وارد حساب کاربری خود شوید</li>
                                            <li>به بخش API دسترسی پیدا کنید</li>
                                            <li>کلید API خود را کپی کرده و در فیلد بالا قرار دهید</li>
                                        </ol>
                                        <div class="setia-help-note">
                                            <strong>نکته:</strong> این کلید برای تولید تصاویر با کیفیت بالا استفاده می‌شود
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تب تولید محتوا -->
            <div class="setia-tab-content" id="tab-content">
                <div class="setia-card">
                    <div class="setia-card-header">
                        <div class="setia-card-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15M9 5C9 6.10457 9.89543 7 11 7H13C14.1046 7 15 6.10457 15 5M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5M12 12H15M12 16H15M9 12H9.01M9 16H9.01" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="setia-card-title">
                            <h2>تنظیمات تولید محتوا</h2>
                            <p>تنظیمات پیش‌فرض برای تولید محتوای متنی</p>
                        </div>
                    </div>
                    <div class="setia-card-content">
                        <!-- تنظیمات لحن و طول -->
                        <div class="setia-field-group">
                            <label for="default_tone" class="setia-label">
                                <div class="setia-label-content">
                                    <div class="setia-label-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M7 8H17M7 12H17M7 16H13M3 5C3 3.89543 3.89543 3 5 3H19C20.1046 3 21 3.89543 21 5V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V5Z" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="setia-label-text">
                                        <span class="setia-label-title">لحن پیش‌فرض محتوا</span>
                                    </div>
                                </div>
                            </label>
                            <select id="default_tone" name="default_tone" class="setia-input">
                                <option value="عادی" <?php selected($default_tone, 'عادی'); ?>>عادی</option>
                                <option value="رسمی" <?php selected($default_tone, 'رسمی'); ?>>رسمی</option>
                                <option value="دوستانه" <?php selected($default_tone, 'دوستانه'); ?>>دوستانه</option>
                                <option value="علمی" <?php selected($default_tone, 'علمی'); ?>>علمی</option>
                                <option value="آموزشی" <?php selected($default_tone, 'آموزشی'); ?>>آموزشی</option>
                            </select>
                        </div>

                        <div class="setia-field-group">
                            <label for="default_length" class="setia-label">
                                <div class="setia-label-content">
                                    <div class="setia-label-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="setia-label-text">
                                        <span class="setia-label-title">طول پیش‌فرض محتوا</span>
                                    </div>
                                </div>
                            </label>
                            <select id="default_length" name="default_length" class="setia-input">
                                <option value="کوتاه" <?php selected($default_length, 'کوتاه'); ?>>کوتاه (100-200 کلمه)</option>
                                <option value="متوسط" <?php selected($default_length, 'متوسط'); ?>>متوسط (300-500 کلمه)</option>
                                <option value="بلند" <?php selected($default_length, 'بلند'); ?>>بلند (600-800 کلمه)</option>
                                <option value="خیلی بلند" <?php selected($default_length, 'خیلی بلند'); ?>>خیلی بلند (1000+ کلمه)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تب تصاویر -->
            <div class="setia-tab-content" id="tab-image">
                <div class="setia-card">
                    <div class="setia-card-header">
                        <div class="setia-card-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M4 16L8.586 11.414C9.367 10.633 10.633 10.633 11.414 11.414L16 16M14 14L15.586 12.414C16.367 11.633 17.633 11.633 18.414 12.414L20 14M14 8H14.01M6 20H18C19.1046 20 20 19.1046 20 18V6C20 4.89543 19.1046 4 18 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20Z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="setia-card-title">
                            <h2>تنظیمات تصاویر</h2>
                            <p>تنظیمات مربوط به تولید و مدیریت تصاویر</p>
                        </div>
                    </div>
                    <div class="setia-card-content">
                        <!-- تنظیمات کیفیت تصویر -->
                        <div class="setia-field-group">
                            <label for="image_quality" class="setia-label">
                                <div class="setia-label-content">
                                    <div class="setia-label-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="setia-label-text">
                                        <span class="setia-label-title">کیفیت پیش‌فرض تصاویر</span>
                                    </div>
                                </div>
                            </label>
                            <select id="image_quality" name="image_quality" class="setia-input">
                                <option value="standard" <?php selected($image_quality, 'standard'); ?>>استاندارد (سریع)</option>
                                <option value="hd" <?php selected($image_quality, 'hd'); ?>>HD (کیفیت بالا)</option>
                                <option value="ultra" <?php selected($image_quality, 'ultra'); ?>>Ultra HD (بهترین کیفیت)</option>
                            </select>
                        </div>

                        <!-- تنظیمات سایز تصویر -->
                        <div class="setia-field-group">
                            <label for="image_size" class="setia-label">
                                <div class="setia-label-content">
                                    <div class="setia-label-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M15 3H21V9M9 21H3V15M21 3L14 10M3 21L10 14" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="setia-label-text">
                                        <span class="setia-label-title">سایز پیش‌فرض تصاویر</span>
                                    </div>
                                </div>
                            </label>
                            <select id="image_size" name="image_size" class="setia-input">
                                <option value="512x512" <?php selected($image_size, '512x512'); ?>>مربع کوچک (512×512)</option>
                                <option value="1024x1024" <?php selected($image_size, '1024x1024'); ?>>مربع متوسط (1024×1024)</option>
                                <option value="1024x768" <?php selected($image_size, '1024x768'); ?>>افقی (1024×768)</option>
                                <option value="768x1024" <?php selected($image_size, '768x1024'); ?>>عمودی (768×1024)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تب سیستم -->
            <div class="setia-tab-content" id="tab-system">
                <div class="setia-card">
                    <div class="setia-card-header">
                        <div class="setia-card-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M10.325 4.317C10.751 2.561 13.249 2.561 13.675 4.317C13.7389 4.5808 13.8642 4.82578 14.0407 5.032C14.2172 5.23822 14.4399 5.39985 14.6907 5.50375C14.9414 5.60764 15.2132 5.65085 15.4838 5.62987C15.7544 5.60889 16.0162 5.5243 16.248 5.383C17.791 4.443 19.558 6.209 18.618 7.753C18.4769 7.98466 18.3924 8.24634 18.3715 8.51677C18.3506 8.78721 18.3938 9.05877 18.4975 9.30938C18.6013 9.55999 18.7627 9.78258 18.9687 9.95905C19.1747 10.1355 19.4194 10.2609 19.683 10.325C21.439 10.751 21.439 13.249 19.683 13.675C19.4192 13.7389 19.1742 13.8642 18.968 14.0407C18.7618 14.2172 18.6001 14.4399 18.4963 14.6907C18.3924 14.9414 18.3491 15.2132 18.3701 15.4838C18.3911 15.7544 18.4757 16.0162 18.617 16.248C19.557 17.791 17.791 19.558 16.247 18.618C16.0153 18.4769 15.7537 18.3924 15.4832 18.3715C15.2128 18.3506 14.9412 18.3938 14.6906 18.4975C14.44 18.6013 14.2174 18.7627 14.0409 18.9687C13.8645 19.1747 13.7391 19.4194 13.675 19.683C13.249 21.439 10.751 21.439 10.325 19.683C10.2611 19.4192 10.1358 19.1742 9.95929 18.968C9.7828 18.7618 9.56011 18.6001 9.30935 18.4963C9.05859 18.3924 8.78683 18.3491 8.51621 18.3701C8.24559 18.3911 7.98375 18.4757 7.752 18.617C6.209 19.557 4.442 17.791 5.382 16.247C5.5231 16.0153 5.60755 15.7537 5.62848 15.4832C5.64942 15.2128 5.60624 14.9412 5.50247 14.6906C5.3987 14.44 5.23726 14.2174 5.03127 14.0409C4.82529 13.8645 4.58056 13.7391 4.317 13.675C2.561 13.249 2.561 10.751 4.317 10.325C4.5808 10.2611 4.82578 10.1358 5.032 9.95929C5.23822 9.7828 5.39985 9.56011 5.50375 9.30935C5.60764 9.05859 5.65085 8.78683 5.62987 8.51621C5.60889 8.24559 5.5243 7.98375 5.383 7.752C4.443 6.209 6.209 4.442 7.753 5.382C7.98466 5.5231 8.24634 5.60755 8.51677 5.62848C8.78721 5.64942 9.05877 5.60624 9.30938 5.50247C9.55999 5.3987 9.78258 5.23726 9.95905 5.03127C10.1355 4.82529 10.2609 4.58056 10.325 4.317Z" stroke="currentColor" stroke-width="2"/>
                                <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="setia-card-title">
                            <h2>تنظیمات سیستم</h2>
                            <p>تنظیمات عمومی و مدیریت سیستم</p>
                        </div>
                    </div>
                    <div class="setia-card-content">
                        <!-- تنظیمات کش -->
                        <div class="setia-field-group">
                            <label for="cache_enabled" class="setia-label">
                                <div class="setia-label-content">
                                    <div class="setia-label-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="setia-label-text">
                                        <span class="setia-label-title">فعال‌سازی کش</span>
                                    </div>
                                </div>
                            </label>
                            <label class="setia-switch">
                                <input type="checkbox" id="cache_enabled" name="cache_enabled" value="1" <?php checked($cache_enabled, '1'); ?>>
                                <span class="setia-switch-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
                    </div>
                </div>

                <div class="setia-section setia-card">
                    <div class="setia-section-header">
                        <div class="setia-section-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/>
                                <path d="M8 12L11 15L16 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="setia-section-title">
                            <h2>تنظیمات عملکرد</h2>
                            <p class="section-description">فعال‌سازی یا غیرفعال‌سازی قابلیت‌های افزونه</p>
                        </div>
                    </div>
                    <div class="setia-section-content">
                        <div class="setia-form-row">
                            <div class="setia-form-group">
                                <label for="enable_seo" class="setia-label">
                                    <span class="label-icon">🔍</span>
                                    تولید متاتگ‌های SEO
                                </label>
                                <div class="setia-toggle-wrapper">
                                    <select id="enable_seo" name="enable_seo" class="setia-select">
                                        <option value="yes" <?php selected($enable_seo, 'yes'); ?>>فعال</option>
                                        <option value="no" <?php selected($enable_seo, 'no'); ?>>غیرفعال</option>
                                    </select>
                                </div>
                            </div>

                            <div class="setia-form-group">
                                <label for="enable_image_generation" class="setia-label">
                                    <span class="label-icon">🖼️</span>
                                    تولید تصویر شاخص
                                </label>
                                <div class="setia-toggle-wrapper">
                                    <select id="enable_image_generation" name="enable_image_generation" class="setia-select">
                                        <option value="yes" <?php selected($enable_image_generation, 'yes'); ?>>فعال</option>
                                        <option value="no" <?php selected($enable_image_generation, 'no'); ?>>غیرفعال</option>
                                    </select>
                                </div>
                                <p class="setia-field-description">
                                    برای تولید تصویر شاخص از هوش مصنوعی استفاده می‌شود. در صورت عدم دسترسی، از سرویس جایگزین استفاده خواهد شد.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="setia-section setia-card">
                    <div class="setia-section-header">
                        <div class="setia-section-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M14.828 14.828L21 21M16.5 10.5C16.5 13.8137 13.8137 16.5 10.5 16.5C7.18629 16.5 4.5 13.8137 4.5 10.5C4.5 7.18629 7.18629 4.5 10.5 4.5C13.8137 4.5 16.5 7.18629 16.5 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="setia-section-title">
                            <h2>تست تولید تصویر</h2>
                            <p class="section-description">آزمایش عملکرد سیستم تولید تصویر</p>
                        </div>
                    </div>
                    <div class="setia-section-content">
                        <div class="setia-test-container">
                            <div class="setia-form-group">
                                <label for="test_prompt" class="setia-label">
                                    <span class="label-icon">💭</span>
                                    موضوع تصویر تست
                                </label>
                                <input type="text" id="test_prompt" placeholder="مثال: منظره طبیعی زیبا در فصل پاییز" class="setia-input">
                                <p class="setia-field-description">موضوعی برای تولید تصویر تست وارد کنید</p>
                            </div>

                            <div class="setia-form-row">
                                <div class="setia-form-group">
                                    <label for="test_image_style" class="setia-label">استایل تست</label>
                                    <select id="test_image_style" class="setia-select">
                                        <option value="realistic">واقع‌گرایانه</option>
                                        <option value="anime">انیمه</option>
                                        <option value="flux-schnell">Flux Schnell</option>
                                        <option value="imagine-turbo">Imagine Turbo</option>
                                    </select>
                                </div>

                                <div class="setia-form-group">
                                    <label for="test_aspect_ratio" class="setia-label">ابعاد تست</label>
                                    <select id="test_aspect_ratio" class="setia-select">
                                        <option value="1:1">مربع (1:1)</option>
                                        <option value="16:9">عریض (16:9)</option>
                                        <option value="9:16">عمودی (9:16)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="setia-test-actions">
                                <button type="button" id="generate_test_image" class="setia-button setia-button-primary">
                                    <span class="button-icon">🎨</span>
                                    تولید تصویر تست
                                </button>
                            </div>

                            <div id="test_image_result" class="setia-test-result" style="display: none;">
                                <div id="test_image_loading" class="setia-loading" style="display: none;">
                                    <div class="loading-spinner"></div>
                                    <span>در حال تولید تصویر...</span>
                                </div>
                                <div id="test_image_preview" class="setia-image-preview"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="setia-section setia-card">
                    <div class="setia-section-header">
                        <div class="setia-section-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 16L4 18C4 19.1046 4.89543 20 6 20L18 20C19.1046 20 20 19.1046 20 18L20 16M16 12L12 16M12 16L8 12M12 16L12 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="setia-section-title">
                            <p class="section-description">تنظیمات پیش‌فرض برای تولید تصویر و زمانبندی</p>
                        </div>
                    </div>
                    <div class="setia-section-content">
                        <div class="setia-form-row">
                            <div class="setia-form-group">
                                <label for="default_image_style" class="setia-label">
                                    <span class="label-icon">🎨</span>
                                    استایل پیش‌فرض تصویر
                                </label>
                                <select id="default_image_style" name="default_image_style" class="setia-select">
                                    <option value="realistic" <?php selected($default_image_style, 'realistic'); ?>>واقع‌گرایانه (Realistic)</option>
                                    <option value="anime" <?php selected($default_image_style, 'anime'); ?>>انیمه (Anime)</option>
                                    <option value="flux-schnell" <?php selected($default_image_style, 'flux-schnell'); ?>>Flux Schnell</option>
                                    <option value="flux-dev-fast" <?php selected($default_image_style, 'flux-dev-fast'); ?>>Flux Dev Fast</option>
                                    <option value="flux-dev" <?php selected($default_image_style, 'flux-dev'); ?>>Flux Dev</option>
                                    <option value="imagine-turbo" <?php selected($default_image_style, 'imagine-turbo'); ?>>Imagine Turbo</option>
                                </select>
                                <p class="setia-field-description">استایلی که به‌صورت پیش‌فرض برای تولید تصویر انتخاب می‌شود</p>
                            </div>

                            <div class="setia-form-group">
                                <label for="default_aspect_ratio" class="setia-label">
                                    <span class="label-icon">📐</span>
                                    ابعاد پیش‌فرض تصویر
                                </label>
                                <select id="default_aspect_ratio" name="default_aspect_ratio" class="setia-select">
                                    <option value="1:1" <?php selected($default_aspect_ratio, '1:1'); ?>>مربع (1:1)</option>
                                    <option value="16:9" <?php selected($default_aspect_ratio, '16:9'); ?>>عریض (16:9)</option>
                                    <option value="9:16" <?php selected($default_aspect_ratio, '9:16'); ?>>عمودی (9:16)</option>
                                    <option value="4:3" <?php selected($default_aspect_ratio, '4:3'); ?>>تلویزیونی (4:3)</option>
                                    <option value="3:4" <?php selected($default_aspect_ratio, '3:4'); ?>>پرتره (3:4)</option>
                                </select>
                            </div>
                        </div>

                        <div class="setia-form-group">
                            <label for="internal_cron_interval" class="setia-label">
                                <span class="label-icon">⏰</span>
                                فاصله کرون داخلی (دقیقه)
                            </label>
                            <div class="setia-input-wrapper">
                                <input type="number" id="internal_cron_interval" name="internal_cron_interval" value="<?php echo esc_attr($internal_cron_interval); ?>" min="5" step="1" class="setia-input setia-input-small">
                                <span class="input-unit">دقیقه</span>
                            </div>
                            <p class="setia-field-description">تعیین می‌کند هر چند دقیقه یک‌بار کرون داخلی وردپرس وظایف افزونه را اجرا کند</p>
                        </div>
                    </div>
                </div>

            <!-- فوتر و دکمه‌های عمل -->
            <div class="setia-footer">
                <div class="setia-footer-actions">
                    <div class="setia-actions-primary">
                        <button type="submit" name="save_settings" class="setia-button setia-button-success">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M19 21H5C3.89543 21 3 20.1046 3 19V5C3 3.89543 3.89543 3 5 3H16L21 8V19C21 20.1046 20.1046 21 19 21Z" stroke="currentColor" stroke-width="2"/>
                                <path d="M17 21V13H7V21M7 3V8H15" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            ذخیره تنظیمات
                        </button>
                        <button type="button" id="test-apis" class="setia-button setia-button-secondary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            تست اتصال API
                        </button>
                    </div>
                    <div class="setia-actions-secondary">
                        <button type="submit" name="clear_cache" class="setia-button setia-button-secondary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M3 6H5H21M19 6V20C19 21.1046 18.1046 22 17 22H7C5.89543 22 5 21.1046 5 20V6M8 6V4C8 2.89543 8.89543 2 10 2H14C15.1046 2 16 2.89543 16 4V6M10 11V17M14 11V17" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            پاک کردن کش
                        </button>
                        <button type="button" id="reset-settings" class="setia-button setia-button-danger">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M4 4V9H4.58152M4.58152 9C5.24618 7.35817 6.43967 5.9735 7.96744 5.08906C9.49521 4.20462 11.2702 3.86894 13.0216 4.12846C14.7731 4.38798 16.4003 5.23157 17.6531 6.53066C18.9058 7.82975 19.7078 9.51055 19.9389 11.2857M4.58152 9H9M20 20V15H19.4185M19.4185 15C18.7538 16.6418 17.5603 18.0265 16.0326 18.9109C14.5048 19.7954 12.7298 20.1311 10.9784 19.8715C9.22692 19.612 7.59966 18.7684 6.34687 17.4693C5.09408 16.1703 4.29213 14.4895 4.06107 12.7143M19.4185 15H15" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            بازنشانی تنظیمات
                        </button>
                    </div>
                </div>
                <div class="setia-footer-info">
                    <p>SETIA Content Generator v2.0 - مولد محتوای هوشمند با قدرت هوش مصنوعی</p>
                </div>
            </div>
            </form>
        </div>
    </div>
</div>

<!-- CSS Loading Debug Script -->
<script type="text/javascript">
// Test CSS file accessibility
fetch('<?php echo plugin_dir_url(__FILE__); ?>../assets/css/admin-settings.css')
    .then(response => {
        console.log('CSS File Status:', response.status);
        if (response.status === 200) {
            console.log('✅ CSS file is accessible');
        } else {
            console.log('❌ CSS file not accessible, status:', response.status);
        }
    })
    .catch(error => {
        console.error('❌ CSS file error:', error);
    });

// Check if CSS is actually loaded
setTimeout(function() {
    var cssLoaded = false;
    var stylesheets = document.styleSheets;
    for (var i = 0; i < stylesheets.length; i++) {
        if (stylesheets[i].href && stylesheets[i].href.includes('admin-settings.css')) {
            cssLoaded = true;
            console.log('✅ CSS Stylesheet Found:', stylesheets[i].href);
            break;
        }
    }
    if (!cssLoaded) {
        console.log('❌ CSS Stylesheet NOT Found in document.styleSheets');
    }
}, 1000);
</script>

<!-- Enhanced JavaScript for improved settings page -->
<script type="text/javascript">
jQuery(document).ready(function($) {
    // Prevent multiple initializations
    if (window.setiaSettingsInitialized) {
        return;
    }
    window.setiaSettingsInitialized = true;

    // Initialize enhanced settings page functionality
    initializeSettingsPage();

    function initializeSettingsPage() {
        // Note: Help toggle functionality is now handled by settings-enhanced.js
        // to prevent conflicts and ensure proper state management

        // API key validation
        $('#gemini_api_key, #imagine_art_api_key').off('input.setia').on('input.setia', function() {
            validateApiKey($(this));
        });

        // Test image generation
        $('#generate_test_image').off('click.setia').on('click.setia', function() {
            generateTestImage();
        });

        // Status indicators update
        updateStatusIndicators();

        // Form validation
        $('form').off('submit.setia').on('submit.setia', function(e) {
            if (!validateForm()) {
                e.preventDefault();
            }
        });

        // Auto-save draft functionality
        var autoSaveTimer;
        $('input, select').off('change.setia').on('change.setia', function() {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(function() {
                showNotification('تغییرات شما ذخیره شد', 'success');
            }, 1000);
        });
    }

    function validateApiKey($input) {
        var value = $input.val();
        var $status = $input.siblings('.input-status');

        if (value.length === 0) {
            $status.removeClass('valid invalid').addClass('empty');
            return;
        }

        var isValid = false;
        if ($input.attr('id') === 'gemini_api_key') {
            isValid = value.startsWith('AIza') && value.length > 20;
        } else if ($input.attr('id') === 'imagine_art_api_key') {
            isValid = value.startsWith('sk-') && value.length > 20;
        }

        $status.removeClass('valid invalid empty').addClass(isValid ? 'valid' : 'invalid');
    }

    function generateTestImage() {
        var prompt = $('#test_prompt').val();
        var style = $('#test_image_style').val();
        var aspectRatio = $('#test_aspect_ratio').val();

        if (!prompt.trim()) {
            showNotification('لطفا موضوعی برای تولید تصویر وارد کنید', 'error');
            return;
        }

        $('#test_image_result').show();
        $('#test_image_loading').show();
        $('#test_image_preview').empty();
        $('#generate_test_image').prop('disabled', true).text('در حال تولید...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'setia_generate_test_image',
                prompt: prompt,
                image_style: style,
                aspect_ratio: aspectRatio,
                nonce: '<?php echo wp_create_nonce('setia_test_connection'); ?>'
            },
            success: function(response) {
                $('#test_image_loading').hide();
                $('#generate_test_image').prop('disabled', false).html('<span class="button-icon">🎨</span>تولید تصویر تست');

                if (response.success) {
                    $('#test_image_preview').html(
                        '<div class="test-image-success">' +
                        '<h4>تصویر با موفقیت تولید شد:</h4>' +
                        '<img src="' + response.data.image_url + '" alt="تصویر تولید شده" class="generated-image">' +
                        '</div>'
                    );
                    showNotification('تصویر با موفقیت تولید شد', 'success');
                } else {
                    $('#test_image_preview').html(
                        '<div class="test-image-error">' +
                        '<p>خطا در تولید تصویر: ' + response.data.message + '</p>' +
                        '</div>'
                    );
                    showNotification('خطا در تولید تصویر', 'error');
                }
            },
            error: function() {
                $('#test_image_loading').hide();
                $('#generate_test_image').prop('disabled', false).html('<span class="button-icon">🎨</span>تولید تصویر تست');
                showNotification('خطا در ارتباط با سرور', 'error');
            }
        });
    }

    function updateStatusIndicators() {
        var geminiKey = $('#gemini_api_key').val();
        var imagineKey = $('#imagine_art_api_key').val();

        $('#gemini-status .status-dot').removeClass('active inactive').addClass(geminiKey ? 'active' : 'inactive');
        $('#imagine-status .status-dot').removeClass('active inactive').addClass(imagineKey ? 'active' : 'inactive');
    }

    function validateForm() {
        var isValid = true;
        var geminiKey = $('#gemini_api_key').val();

        if (!geminiKey) {
            showNotification('کلید API Gemini الزامی است', 'error');
            isValid = false;
        }

        return isValid;
    }

    function showNotification(message, type) {
        var $notification = $('<div class="setia-notification setia-notification-' + type + '">' + message + '</div>');
        $('body').append($notification);

        setTimeout(function() {
            $notification.addClass('show');
        }, 100);

        setTimeout(function() {
            $notification.removeClass('show');
            setTimeout(function() {
                $notification.remove();
            }, 300);
        }, 3000);
    }

    // Initialize on page load
    updateStatusIndicators();

    // Update status indicators when API keys change
    $('#gemini_api_key, #imagine_art_api_key').on('input', function() {
        setTimeout(updateStatusIndicators, 100);
    });
});
</script>