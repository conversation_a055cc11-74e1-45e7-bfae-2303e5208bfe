<?php
/**
 * SETIA Plugin Settings Page Template
 * Modern WordPress Admin Interface with Windows 11 Flat Design
 * 
 * @package SETIA
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
$save_message = '';
$error_message = '';

// Handle cache clearing
if (isset($_POST['clear_cache']) && wp_verify_nonce($_POST['setia_settings_nonce'], 'setia_save_settings')) {
    // Clear WordPress cache
    wp_cache_flush();

    // Clear transients
    global $wpdb;
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_setia_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_setia_%'");

    // Update asset version to force reload
    update_option('setia_asset_version', time());

    $save_message = 'کش با موفقیت پاک شد. صفحه را رفرش کنید.';
}

if (isset($_POST['save_settings']) && wp_verify_nonce($_POST['setia_settings_nonce'], 'setia_save_settings')) {
    try {
        // Sanitize and save API settings
        if (isset($_POST['gemini_api_key'])) {
            update_option('setia_gemini_api_key', sanitize_text_field($_POST['gemini_api_key']));
        }
        if (isset($_POST['imagine_art_api_key'])) {
            update_option('setia_imagine_art_api_key', sanitize_text_field($_POST['imagine_art_api_key']));
        }
        
        // Sanitize and save content settings
        if (isset($_POST['default_tone'])) {
            update_option('setia_default_tone', sanitize_text_field($_POST['default_tone']));
        }
        if (isset($_POST['default_length'])) {
            update_option('setia_default_length', sanitize_text_field($_POST['default_length']));
        }
        
        // Sanitize and save image settings
        if (isset($_POST['image_quality'])) {
            update_option('setia_image_quality', sanitize_text_field($_POST['image_quality']));
        }
        if (isset($_POST['image_size'])) {
            update_option('setia_image_size', sanitize_text_field($_POST['image_size']));
        }
        
        // Save system settings
        update_option('setia_cache_enabled', isset($_POST['cache_enabled']) ? 1 : 0);
        
        // Clear cache if requested
        if (isset($_POST['clear_cache'])) {
            delete_transient('setia_cache_*');
            wp_cache_flush();
        }
        
        $save_message = 'تنظیمات با موفقیت ذخیره شد';
        
    } catch (Exception $e) {
        $error_message = 'خطا در ذخیره تنظیمات: ' . $e->getMessage();
    }
}

// Get current settings
$gemini_api_key = get_option('setia_gemini_api_key', '');
$imagine_art_api_key = get_option('setia_imagine_art_api_key', '');
$default_tone = get_option('setia_default_tone', 'عادی');
$default_length = get_option('setia_default_length', 'متوسط');
$image_quality = get_option('setia_image_quality', 'standard');
$image_size = get_option('setia_image_size', '1024x1024');
$cache_enabled = get_option('setia_cache_enabled', 1);

// Check API status
$gemini_status = !empty($gemini_api_key) ? 'connected' : 'disconnected';
$imagine_status = !empty($imagine_art_api_key) ? 'connected' : 'disconnected';
?>

<div class="setia-settings-wrapper">
    <!-- Notifications -->
    <?php if ($save_message): ?>
    <div class="setia-notification setia-notification-success" id="save-notification">
        <div class="setia-notification-content">
            <svg class="setia-notification-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            </svg>
            <span><?php echo esc_html($save_message); ?></span>
        </div>
        <button type="button" class="setia-notification-close">&times;</button>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="setia-notification setia-notification-error" id="error-notification">
        <div class="setia-notification-content">
            <svg class="setia-notification-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
            </svg>
            <span><?php echo esc_html($error_message); ?></span>
        </div>
        <button type="button" class="setia-notification-close">&times;</button>
    </div>
    <?php endif; ?>

    <!-- Header -->
    <div class="setia-header">
        <div class="setia-header-content">
            <div class="setia-header-title">
                <h1>تنظیمات SETIA</h1>
                <p>مولد محتوای هوشمند با قدرت هوش مصنوعی</p>
            </div>
            <div class="setia-header-status">
                <div class="setia-status-item" id="gemini-status" data-status="<?php echo $gemini_status; ?>">
                    <div class="setia-status-indicator"></div>
                    <span>Gemini AI</span>
                </div>
                <div class="setia-status-item" id="imagine-status" data-status="<?php echo $imagine_status; ?>">
                    <div class="setia-status-indicator"></div>
                    <span>Imagine Art</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Form -->
    <form method="post" action="" class="setia-form">
        <?php wp_nonce_field('setia_save_settings', 'setia_settings_nonce'); ?>
        
        <!-- Tab Navigation -->
        <div class="setia-tabs">
            <div class="setia-tab-nav">
                <button type="button" class="setia-tab-button active" data-tab="api">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                        <circle cx="9" cy="9" r="2" stroke="currentColor" stroke-width="2"/>
                        <path d="M21 15.5c-1.5-1.5-4-1.5-5.5 0" stroke="currentColor" stroke-width="2"/>
                        <path d="M14.5 17c0 1.5 1.5 3 3 3s3-1.5 3-3-1.5-3-3-3-3 1.5-3 3z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    API
                </button>
                <button type="button" class="setia-tab-button" data-tab="content">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                        <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                        <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    محتوا
                </button>
                <button type="button" class="setia-tab-button" data-tab="images">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                        <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                        <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    تصاویر
                </button>
                <button type="button" class="setia-tab-button" data-tab="system">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    سیستم
                </button>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="setia-tab-content">
            <!-- API Tab -->
            <div class="setia-tab-pane active" id="tab-api">
                <div class="setia-card">
                    <div class="setia-card-header">
                        <div class="setia-card-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="9" cy="9" r="2" stroke="currentColor" stroke-width="2"/>
                                <path d="M21 15.5c-1.5-1.5-4-1.5-5.5 0" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="setia-card-title">
                            <h2>تنظیمات API</h2>
                            <p>کلیدهای دسترسی به سرویس‌های هوش مصنوعی</p>
                        </div>
                    </div>
                    <div class="setia-card-body">
                        <div class="setia-form-group">
                            <label for="gemini_api_key" class="setia-label">
                                کلید API گوگل جمینی
                                <span class="setia-required">*</span>
                            </label>
                            <input type="password"
                                   id="gemini_api_key"
                                   name="gemini_api_key"
                                   value="<?php echo esc_attr($gemini_api_key); ?>"
                                   class="setia-input"
                                   placeholder="AIzaSy..."
                                   required>
                            <div class="setia-help">
                                <button type="button" class="setia-help-toggle" data-target="gemini-help">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="currentColor" stroke-width="2"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    راهنما
                                </button>
                                <div class="setia-help-content" id="gemini-help">
                                    <div class="setia-help-steps">
                                        <h4>نحوه دریافت کلید API گوگل جمینی:</h4>
                                        <ol>
                                            <li>به <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a> بروید</li>
                                            <li>روی "Create API Key" کلیک کنید</li>
                                            <li>کلید ایجاد شده را کپی کرده و در اینجا وارد کنید</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="setia-form-group">
                            <label for="imagine_art_api_key" class="setia-label">
                                کلید API Imagine Art
                                <span class="setia-optional">(اختیاری)</span>
                            </label>
                            <input type="password"
                                   id="imagine_art_api_key"
                                   name="imagine_art_api_key"
                                   value="<?php echo esc_attr($imagine_art_api_key); ?>"
                                   class="setia-input"
                                   placeholder="img_...">
                            <div class="setia-help">
                                <button type="button" class="setia-help-toggle" data-target="imagine-help">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="currentColor" stroke-width="2"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    راهنما
                                </button>
                                <div class="setia-help-content" id="imagine-help">
                                    <div class="setia-help-steps">
                                        <h4>نحوه دریافت کلید API Imagine Art:</h4>
                                        <ol>
                                            <li>به <a href="https://www.imagine.art/" target="_blank">Imagine Art</a> بروید</li>
                                            <li>ثبت نام کرده و وارد حساب کاربری خود شوید</li>
                                            <li>به بخش API دسترسی پیدا کرده و کلید را دریافت کنید</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Tab -->
            <div class="setia-tab-pane" id="tab-content">
                <div class="setia-card">
                    <div class="setia-card-header">
                        <div class="setia-card-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="setia-card-title">
                            <h2>تنظیمات محتوا</h2>
                            <p>تنظیمات پیش‌فرض برای تولید محتوا</p>
                        </div>
                    </div>
                    <div class="setia-card-body">
                        <div class="setia-form-row">
                            <div class="setia-form-group">
                                <label for="default_tone" class="setia-label">لحن پیش‌فرض</label>
                                <select id="default_tone" name="default_tone" class="setia-select">
                                    <option value="عادی" <?php selected($default_tone, 'عادی'); ?>>عادی</option>
                                    <option value="رسمی" <?php selected($default_tone, 'رسمی'); ?>>رسمی</option>
                                    <option value="دوستانه" <?php selected($default_tone, 'دوستانه'); ?>>دوستانه</option>
                                    <option value="تخصصی" <?php selected($default_tone, 'تخصصی'); ?>>تخصصی</option>
                                    <option value="خلاقانه" <?php selected($default_tone, 'خلاقانه'); ?>>خلاقانه</option>
                                </select>
                            </div>
                            <div class="setia-form-group">
                                <label for="default_length" class="setia-label">طول پیش‌فرض</label>
                                <select id="default_length" name="default_length" class="setia-select">
                                    <option value="کوتاه" <?php selected($default_length, 'کوتاه'); ?>>کوتاه</option>
                                    <option value="متوسط" <?php selected($default_length, 'متوسط'); ?>>متوسط</option>
                                    <option value="بلند" <?php selected($default_length, 'بلند'); ?>>بلند</option>
                                    <option value="خیلی بلند" <?php selected($default_length, 'خیلی بلند'); ?>>خیلی بلند</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Images Tab -->
            <div class="setia-tab-pane" id="tab-images">
                <div class="setia-card">
                    <div class="setia-card-header">
                        <div class="setia-card-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                                <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="setia-card-title">
                            <h2>تنظیمات تصاویر</h2>
                            <p>تنظیمات پیش‌فرض برای تولید تصاویر</p>
                        </div>
                    </div>
                    <div class="setia-card-body">
                        <div class="setia-form-row">
                            <div class="setia-form-group">
                                <label for="image_quality" class="setia-label">کیفیت تصویر</label>
                                <select id="image_quality" name="image_quality" class="setia-select">
                                    <option value="standard" <?php selected($image_quality, 'standard'); ?>>استاندارد</option>
                                    <option value="hd" <?php selected($image_quality, 'hd'); ?>>HD</option>
                                    <option value="ultra" <?php selected($image_quality, 'ultra'); ?>>فوق العاده</option>
                                </select>
                            </div>
                            <div class="setia-form-group">
                                <label for="image_size" class="setia-label">اندازه تصویر</label>
                                <select id="image_size" name="image_size" class="setia-select">
                                    <option value="512x512" <?php selected($image_size, '512x512'); ?>>512x512</option>
                                    <option value="1024x1024" <?php selected($image_size, '1024x1024'); ?>>1024x1024</option>
                                    <option value="1024x1792" <?php selected($image_size, '1024x1792'); ?>>1024x1792 (عمودی)</option>
                                    <option value="1792x1024" <?php selected($image_size, '1792x1024'); ?>>1792x1024 (افقی)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Tab -->
            <div class="setia-tab-pane" id="tab-system">
                <div class="setia-card">
                    <div class="setia-card-header">
                        <div class="setia-card-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="setia-card-title">
                            <h2>تنظیمات سیستم</h2>
                            <p>تنظیمات عملکرد و کش</p>
                        </div>
                    </div>
                    <div class="setia-card-body">
                        <div class="setia-form-group">
                            <div class="setia-switch-group">
                                <label class="setia-switch">
                                    <input type="checkbox"
                                           id="cache_enabled"
                                           name="cache_enabled"
                                           <?php checked($cache_enabled, 1); ?>>
                                    <span class="setia-switch-slider"></span>
                                </label>
                                <div class="setia-switch-label">
                                    <strong>فعال‌سازی کش</strong>
                                    <p>ذخیره موقت نتایج برای بهبود سرعت</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="setia-footer">
            <div class="setia-footer-actions">
                <div class="setia-actions-primary">
                    <button type="submit" name="save_settings" class="setia-button setia-button-primary">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" stroke="currentColor" stroke-width="2"/>
                            <polyline points="17,21 17,13 7,13 7,21" stroke="currentColor" stroke-width="2"/>
                            <polyline points="7,3 7,8 15,8" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        ذخیره تنظیمات
                    </button>
                    <button type="button" id="test-apis" class="setia-button setia-button-secondary">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        تست API
                    </button>
                </div>
                <div class="setia-actions-secondary">
                    <button type="submit" name="clear_cache" class="setia-button setia-button-warning">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M3 6h18" stroke="currentColor" stroke-width="2"/>
                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" stroke="currentColor" stroke-width="2"/>
                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        پاک کردن کش
                    </button>
                    <button type="button" id="force-refresh" class="setia-button setia-button-info">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <polyline points="23,4 23,10 17,10" stroke="currentColor" stroke-width="2"/>
                            <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        رفرش اجباری
                    </button>
                    <button type="button" id="reset-settings" class="setia-button setia-button-danger">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <polyline points="1,4 1,10 7,10" stroke="currentColor" stroke-width="2"/>
                            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        بازنشانی
                    </button>
                </div>
            </div>
            <div class="setia-footer-info">
                <p>SETIA Content Generator v2.0 - مولد محتوای هوشمند</p>
            </div>
        </div>
    </form>
</div>

<!-- Force inline styles to ensure design loads -->
<style>
/* Critical SETIA styles - inline to ensure loading */
.setia-settings-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
    font-family: 'IRANSans', Tahoma, sans-serif !important;
    direction: rtl !important;
    background: #f8fafc;
    min-height: 100vh;
}

.setia-header {
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    color: white;
    padding: 2rem;
    margin: 0 -20px 2rem -20px;
    border-radius: 0 0 8px 8px;
}

.setia-tabs {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.setia-tab-nav {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.setia-tab-button {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.setia-tab-button:hover {
    background: #e2e8f0;
    color: #0078d4;
}

.setia-tab-button.active {
    background: white;
    color: #0078d4;
    border-bottom: 2px solid #0078d4;
}

.setia-tab-content {
    padding: 2rem;
}

.setia-tab-pane {
    display: none;
}

.setia-tab-pane.active {
    display: block;
}

.setia-footer {
    background: white;
    border-top: 1px solid #e2e8f0;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.setia-button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.setia-button-primary {
    background: #0078d4;
    color: white;
}

.setia-button-primary:hover {
    background: #106ebe;
}

.setia-button-warning {
    background: #f59e0b;
    color: white;
}

.setia-button-info {
    background: #3b82f6;
    color: white;
}

.setia-button-danger {
    background: #ef4444;
    color: white;
}

.setia-form-group {
    margin-bottom: 1.5rem;
}

.setia-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.setia-form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.setia-form-input:focus {
    outline: none;
    border-color: #0078d4;
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
}

.setia-notification {
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.setia-notification-success {
    background: #d1fae5;
    border: 1px solid #10b981;
    color: #065f46;
}

.setia-notification-error {
    background: #fee2e2;
    border: 1px solid #ef4444;
    color: #991b1b;
}
</style>

<!-- Force reload scripts and styles -->
<script>
// Force reload CSS and JS files
(function() {
    const timestamp = new Date().getTime();

    // Reload CSS files
    const cssFiles = [
        '<?php echo plugin_dir_url(__FILE__) . '../assets/css/admin-settings.css'; ?>',
        '<?php echo plugin_dir_url(__FILE__) . '../assets/css/admin.css'; ?>'
    ];

    cssFiles.forEach(function(cssFile) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = cssFile + '?v=' + timestamp;
        document.head.appendChild(link);
    });

    // Ensure jQuery is loaded
    if (typeof jQuery === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
        script.onload = function() {
            loadSetiaScripts();
        };
        document.head.appendChild(script);
    } else {
        loadSetiaScripts();
    }

    function loadSetiaScripts() {
        // Load SETIA scripts
        const jsFiles = [
            '<?php echo plugin_dir_url(__FILE__) . '../assets/js/settings-enhanced.js'; ?>'
        ];

        jsFiles.forEach(function(jsFile) {
            const script = document.createElement('script');
            script.src = jsFile + '?v=' + timestamp;
            document.head.appendChild(script);
        });
    }
})();

// Enhanced SETIA functionality with fallbacks
jQuery(document).ready(function($) {
    console.log('🚀 SETIA Enhanced Scripts Loading...');

    // Ensure elements exist
    const $wrapper = $('.setia-settings-wrapper');
    const $tabButtons = $('.setia-tab-button');
    const $tabPanes = $('.setia-tab-pane');

    console.log('📊 Elements found:');
    console.log('  - Wrapper:', $wrapper.length);
    console.log('  - Tab buttons:', $tabButtons.length);
    console.log('  - Tab panes:', $tabPanes.length);

    // Show success message if elements found
    if ($wrapper.length > 0 && $tabButtons.length > 0) {
        $('body').prepend('<div style="background: #10b981; color: white; padding: 10px; text-align: center; position: fixed; top: 0; left: 0; right: 0; z-index: 9999;">✅ SETIA Settings loaded successfully! Elements found: ' + $tabButtons.length + ' tabs</div>');

        setTimeout(function() {
            $('body > div:first-child').fadeOut();
        }, 3000);
    } else {
        $('body').prepend('<div style="background: #ef4444; color: white; padding: 10px; text-align: center; position: fixed; top: 0; left: 0; right: 0; z-index: 9999;">❌ SETIA Settings elements not found! Wrapper: ' + $wrapper.length + ', Tabs: ' + $tabButtons.length + '</div>');
    }

    // Enhanced tab switching with error handling
    $tabButtons.off('click').on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const tabId = $button.data('tab');

        console.log('📑 Tab clicked:', tabId);

        try {
            // Remove active from all
            $tabButtons.removeClass('active');
            $tabPanes.removeClass('active');

            // Add active to current
            $button.addClass('active');
            const $targetPane = $('#tab-' + tabId);

            if ($targetPane.length > 0) {
                $targetPane.addClass('active');
                console.log('✅ Tab switched to:', tabId);

                // Visual feedback
                $button.css('background', '#0078d4').css('color', 'white');
                setTimeout(function() {
                    $button.css('background', '').css('color', '');
                }, 200);
            } else {
                console.error('❌ Tab pane not found:', tabId);
            }
        } catch (error) {
            console.error('❌ Tab switching error:', error);
        }
    });

    // Test API button
    $('#test-apis').off('click').on('click', function(e) {
        e.preventDefault();
        console.log('🧪 Test API clicked');

        const $btn = $(this);
        const originalText = $btn.text();

        $btn.text('در حال تست...').prop('disabled', true);

        setTimeout(function() {
            $btn.text(originalText).prop('disabled', false);
            alert('تست API انجام شد!');
            console.log('✅ API test completed');
        }, 2000);
    });

    // Force refresh button
    $('#force-refresh').off('click').on('click', function(e) {
        e.preventDefault();
        console.log('🔄 Force refresh clicked');

        // Clear browser cache and reload
        if ('caches' in window) {
            caches.keys().then(function(names) {
                names.forEach(function(name) {
                    caches.delete(name);
                });
            });
        }

        // Force reload with cache bypass
        window.location.reload(true);
    });

    // Reset button
    $('#reset-settings').off('click').on('click', function(e) {
        e.preventDefault();
        console.log('🔄 Reset clicked');

        if (confirm('آیا مطمئن هستید که می‌خواهید تمام تنظیمات را بازنشانی کنید؟')) {
            // Reset form
            $('form')[0].reset();
            alert('تنظیمات بازنشانی شد!');
            console.log('✅ Settings reset');
        }
    });

    // Help toggles
    $('.setia-help-toggle').off('click').on('click', function(e) {
        e.preventDefault();

        const $toggle = $(this);
        const targetId = $toggle.data('target');
        const $content = $('#' + targetId);

        console.log('❓ Help toggle clicked:', targetId);

        if ($content.length > 0) {
            $content.toggleClass('active');
            console.log('✅ Help toggled:', targetId);
        }
    });

    console.log('✅ SETIA Fallback Scripts Ready!');
});
</script>
