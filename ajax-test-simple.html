<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست سریع سیستم AJAX بهینه‌سازی شده SETIA</title>
    <style>
        body {
            font-family: 'Vazir', '<PERSON><PERSON><PERSON>', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2271b1;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-button {
            background: #2271b1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .test-button:hover {
            background: #135e96;
            transform: translateY(-2px);
        }
        
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #2271b1;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .loading::after {
            content: " ⏳";
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 تست سریع سیستم AJAX بهینه‌سازی شده SETIA</h1>
        
        <div class="test-section">
            <h3>📊 آمار عملکرد سیستم</h3>
            <div class="metrics" id="metrics">
                <div class="metric-card">
                    <div class="metric-value" id="total-requests">0</div>
                    <div class="metric-label">کل درخواست‌ها</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="successful-requests">0</div>
                    <div class="metric-label">درخواست‌های موفق</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="failed-requests">0</div>
                    <div class="metric-label">درخواست‌های ناموفق</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="avg-response-time">0ms</div>
                    <div class="metric-label">میانگین زمان پاسخ</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="cache-hits">0</div>
                    <div class="metric-label">Cache Hits</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="queue-size">0</div>
                    <div class="metric-label">اندازه صف</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 تست‌های سریع</h3>
            <button class="test-button" onclick="testBasicConnection()">تست اتصال پایه</button>
            <button class="test-button" onclick="testWithCache()">تست با کش</button>
            <button class="test-button" onclick="testConcurrency()">تست همزمانی</button>
            <button class="test-button" onclick="testErrorHandling()">تست مدیریت خطا</button>
            <button class="test-button" onclick="clearResults()">پاک کردن نتایج</button>
            <button class="test-button" onclick="updateMetrics()">بروزرسانی آمار</button>
        </div>
        
        <div class="test-section">
            <h3>📋 نتایج تست‌ها</h3>
            <div id="results"></div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/ajax-optimized.js"></script>
    <script>
        // شبیه‌سازی محیط WordPress
        window.setiaParams = {
            nonce: 'test_nonce_12345',
            debug: true
        };

        // تابع کمکی برای نمایش نتایج
        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('fa-IR');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = `[${timestamp}] ${message}`;
            document.getElementById('results').appendChild(resultDiv);
            
            // اسکرول به پایین
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // تست اتصال پایه
        async function testBasicConnection() {
            const button = event.target;
            button.classList.add('loading');
            button.disabled = true;
            
            try {
                addResult('شروع تست اتصال پایه...', 'info');
                
                const response = await window.SetiaAjax.testConnection({
                    test_data: 'سلام از سیستم AJAX بهینه‌سازی شده'
                });
                
                addResult('✅ تست اتصال پایه موفق: ' + JSON.stringify(response), 'success');
                
            } catch (error) {
                addResult('❌ خطا در تست اتصال: ' + error.message, 'error');
            } finally {
                button.classList.remove('loading');
                button.disabled = false;
                updateMetrics();
            }
        }

        // تست با کش
        async function testWithCache() {
            const button = event.target;
            button.classList.add('loading');
            button.disabled = true;
            
            try {
                addResult('شروع تست کش...', 'info');
                
                // درخواست اول - باید از سرور بیاید
                const start1 = performance.now();
                await window.SetiaAjax.testConnection({ cache_test: true });
                const time1 = Math.round(performance.now() - start1);
                addResult(`درخواست اول: ${time1}ms (از سرور)`, 'info');
                
                // درخواست دوم - باید از کش بیاید
                const start2 = performance.now();
                await window.SetiaAjax.testConnection({ cache_test: true });
                const time2 = Math.round(performance.now() - start2);
                addResult(`درخواست دوم: ${time2}ms (از کش)`, 'success');
                
                if (time2 < time1) {
                    addResult('✅ کش به درستی کار می‌کند', 'success');
                } else {
                    addResult('⚠️ کش ممکن است درست کار نکند', 'error');
                }
                
            } catch (error) {
                addResult('❌ خطا در تست کش: ' + error.message, 'error');
            } finally {
                button.classList.remove('loading');
                button.disabled = false;
                updateMetrics();
            }
        }

        // تست همزمانی
        async function testConcurrency() {
            const button = event.target;
            button.classList.add('loading');
            button.disabled = true;
            
            try {
                addResult('شروع تست همزمانی (5 درخواست)...', 'info');
                
                const promises = [];
                for (let i = 1; i <= 5; i++) {
                    promises.push(
                        window.SetiaAjax.testConnection({ 
                            concurrent_test: true, 
                            request_id: i 
                        })
                    );
                }
                
                const results = await Promise.all(promises);
                addResult(`✅ همه ${results.length} درخواست همزمان موفق بودند`, 'success');
                
            } catch (error) {
                addResult('❌ خطا در تست همزمانی: ' + error.message, 'error');
            } finally {
                button.classList.remove('loading');
                button.disabled = false;
                updateMetrics();
            }
        }

        // تست مدیریت خطا
        async function testErrorHandling() {
            const button = event.target;
            button.classList.add('loading');
            button.disabled = true;
            
            try {
                addResult('شروع تست مدیریت خطا...', 'info');
                
                // تست درخواست نامعتبر
                try {
                    await window.SetiaAjax.request('invalid_action_test', {});
                    addResult('⚠️ درخواست نامعتبر نباید موفق می‌شد', 'error');
                } catch (error) {
                    addResult('✅ درخواست نامعتبر به درستی رد شد: ' + error.message, 'success');
                }
                
            } catch (error) {
                addResult('❌ خطا در تست مدیریت خطا: ' + error.message, 'error');
            } finally {
                button.classList.remove('loading');
                button.disabled = false;
                updateMetrics();
            }
        }

        // بروزرسانی آمار
        function updateMetrics() {
            if (window.SetiaAjax) {
                const metrics = window.SetiaAjax.getMetrics();
                
                document.getElementById('total-requests').textContent = metrics.totalRequests;
                document.getElementById('successful-requests').textContent = metrics.successfulRequests;
                document.getElementById('failed-requests').textContent = metrics.failedRequests;
                document.getElementById('avg-response-time').textContent = Math.round(metrics.averageResponseTime) + 'ms';
                document.getElementById('cache-hits').textContent = metrics.cachedRequests;
                document.getElementById('queue-size').textContent = metrics.queueSize;
            }
        }

        // پاک کردن نتایج
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            if (window.SetiaAjax) {
                window.SetiaAjax.resetMetrics();
                window.SetiaAjax.clearCache();
                updateMetrics();
            }
            addResult('نتایج و آمار پاک شدند', 'info');
        }

        // بروزرسانی آمار هر 5 ثانیه
        setInterval(updateMetrics, 5000);
        
        // بروزرسانی اولیه
        setTimeout(updateMetrics, 1000);
    </script>
</body>
</html>
