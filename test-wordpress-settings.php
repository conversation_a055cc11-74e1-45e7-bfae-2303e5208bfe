<?php
/**
 * Test WordPress Settings Page
 * تست مستقیم صفحه تنظیمات در محیط WordPress
 */

// WordPress environment
if (file_exists('../../../wp-config.php')) {
    require_once('../../../wp-config.php');
    require_once('../../../wp-load.php');
} else {
    die('WordPress not found');
}

if (!current_user_can('manage_options')) {
    die('Access denied');
}

// Force clear cache and update asset version
wp_cache_flush();
update_option('setia_asset_version', time());

// Simulate WordPress admin environment
global $wp_scripts, $wp_styles;

// Initialize WordPress admin
if (!function_exists('get_current_screen')) {
    require_once(ABSPATH . 'wp-admin/includes/screen.php');
}

// Set up admin environment
set_current_screen('setia-content-generator_page_setia-settings');

// Load the plugin
if (file_exists(__DIR__ . '/setia-content-generator.php')) {
    include_once(__DIR__ . '/setia-content-generator.php');
}

?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SETIA WordPress Settings Test</title>
    
    <!-- WordPress Admin Styles -->
    <link rel="stylesheet" href="<?php echo admin_url('css/common.css'); ?>">
    <link rel="stylesheet" href="<?php echo admin_url('css/admin-menu.css'); ?>">
    <link rel="stylesheet" href="<?php echo admin_url('css/dashboard.css'); ?>">
    
    <!-- Force load SETIA styles -->
    <link rel="stylesheet" href="assets/css/admin-settings.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="assets/css/admin.css?v=<?php echo time(); ?>">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        body {
            background: #f1f1f1;
            font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
            margin: 0;
            padding: 20px;
        }
        .wrap {
            margin: 10px 0 0 0;
            max-width: none;
        }
        .test-header {
            background: #0073aa;
            color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .debug-info {
            background: #fff;
            border: 1px solid #ccd0d4;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
    </style>
</head>
<body class="wp-admin">
    <div class="wrap">
        <div class="test-header">
            <h1>🧪 SETIA WordPress Settings Test</h1>
            <p>تست مستقیم صفحه تنظیمات در محیط WordPress</p>
            <p>زمان: <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <div class="debug-info">
            <h3>📊 اطلاعات Debug</h3>
            <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
            <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
            <p><strong>Asset Version:</strong> <?php echo get_option('setia_asset_version', 'not set'); ?></p>
            <p><strong>Current Screen:</strong> <?php echo get_current_screen()->id ?? 'not set'; ?></p>
            <p><strong>Plugin URL:</strong> <?php echo plugin_dir_url(__FILE__); ?></p>
        </div>

        <?php
        // Include the actual settings page
        if (file_exists(__DIR__ . '/templates/settings-page.php')) {
            echo '<div class="settings-container">';
            include(__DIR__ . '/templates/settings-page.php');
            echo '</div>';
        } else {
            echo '<div class="error"><p>فایل settings-page.php یافت نشد!</p></div>';
        }
        ?>
    </div>

    <!-- Force load SETIA JavaScript -->
    <script src="assets/js/settings-enhanced.js?v=<?php echo time(); ?>"></script>
    
    <script>
        console.log('🚀 WordPress Settings Test - Loading...');
        console.log('jQuery version:', jQuery.fn.jquery);
        console.log('WordPress admin environment loaded');
        
        jQuery(document).ready(function($) {
            console.log('📱 WordPress Settings Test - Ready');
            
            // Check for SETIA elements
            const $wrapper = $('.setia-settings-wrapper');
            const $tabs = $('.setia-tab-button');
            const $panes = $('.setia-tab-pane');
            
            console.log('🔍 SETIA Elements Found:');
            console.log('  - Settings wrapper:', $wrapper.length);
            console.log('  - Tab buttons:', $tabs.length);
            console.log('  - Tab panes:', $panes.length);
            
            if ($wrapper.length === 0) {
                console.error('❌ SETIA settings wrapper not found!');
                $('body').prepend('<div style="background: red; color: white; padding: 10px; margin: 10px 0;">❌ SETIA settings wrapper not found!</div>');
            } else {
                console.log('✅ SETIA settings wrapper found');
                $('body').prepend('<div style="background: green; color: white; padding: 10px; margin: 10px 0;">✅ SETIA settings loaded successfully</div>');
            }
            
            // Test tab functionality
            if ($tabs.length > 0) {
                console.log('🧪 Testing tab functionality...');
                
                $tabs.each(function(index) {
                    const $tab = $(this);
                    const tabId = $tab.data('tab');
                    console.log(`  - Tab ${index + 1}: ${tabId}`);
                });
                
                // Test clicking first tab
                setTimeout(function() {
                    console.log('🖱️ Testing tab click...');
                    $tabs.first().trigger('click');
                }, 1000);
            }
        });
    </script>
</body>
</html>
