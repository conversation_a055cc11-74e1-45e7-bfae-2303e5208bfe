<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SETIA Settings Test</title>
    <link rel="stylesheet" href="assets/css/admin-settings.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="setia-settings-wrapper">
        <!-- Test Notification -->
        <div class="setia-notification setia-notification-success" id="save-notification">
            <div class="setia-notification-content">
                <svg class="setia-notification-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>تنظیمات با موفقیت ذخیره شد</span>
            </div>
            <button type="button" class="setia-notification-close">&times;</button>
        </div>

        <!-- Header -->
        <div class="setia-header">
            <div class="setia-header-content">
                <div class="setia-header-title">
                    <h1>تنظیمات SETIA</h1>
                    <p>مولد محتوای هوشمند با قدرت هوش مصنوعی</p>
                </div>
                <div class="setia-header-status">
                    <div class="setia-status-item" id="gemini-status" data-status="connected">
                        <div class="setia-status-indicator"></div>
                        <span>Gemini AI</span>
                    </div>
                    <div class="setia-status-item" id="imagine-status" data-status="disconnected">
                        <div class="setia-status-indicator"></div>
                        <span>Imagine Art</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Form -->
        <form method="post" action="" class="setia-form">
            <!-- Tab Navigation -->
            <div class="setia-tabs">
                <div class="setia-tab-nav">
                    <button type="button" class="setia-tab-button active" data-tab="api">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                            <circle cx="9" cy="9" r="2" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        API
                    </button>
                    <button type="button" class="setia-tab-button" data-tab="content">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        محتوا
                    </button>
                    <button type="button" class="setia-tab-button" data-tab="images">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        تصاویر
                    </button>
                    <button type="button" class="setia-tab-button" data-tab="system">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        سیستم
                    </button>
                </div>
            </div>

            <!-- Tab Content -->
            <div class="setia-tab-content">
                <!-- API Tab -->
                <div class="setia-tab-pane active" id="tab-api">
                    <div class="setia-card">
                        <div class="setia-card-header">
                            <div class="setia-card-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </div>
                            <div class="setia-card-title">
                                <h2>تنظیمات API</h2>
                                <p>کلیدهای دسترسی به سرویس‌های هوش مصنوعی</p>
                            </div>
                        </div>
                        <div class="setia-card-body">
                            <div class="setia-form-group">
                                <label for="gemini_api_key" class="setia-label">
                                    کلید API گوگل جمینی
                                    <span class="setia-required">*</span>
                                </label>
                                <input type="password" 
                                       id="gemini_api_key" 
                                       name="gemini_api_key" 
                                       value="" 
                                       class="setia-input" 
                                       placeholder="AIzaSy..." 
                                       required>
                                <div class="setia-help">
                                    <button type="button" class="setia-help-toggle" data-target="gemini-help">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="currentColor" stroke-width="2"/>
                                            <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        راهنما
                                    </button>
                                    <div class="setia-help-content" id="gemini-help">
                                        <div class="setia-help-steps">
                                            <h4>نحوه دریافت کلید API گوگل جمینی:</h4>
                                            <ol>
                                                <li>به <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a> بروید</li>
                                                <li>روی "Create API Key" کلیک کنید</li>
                                                <li>کلید ایجاد شده را کپی کرده و در اینجا وارد کنید</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="setia-form-group">
                                <label for="imagine_art_api_key" class="setia-label">
                                    کلید API Imagine Art
                                    <span class="setia-optional">(اختیاری)</span>
                                </label>
                                <input type="password"
                                       id="imagine_art_api_key"
                                       name="imagine_art_api_key"
                                       value=""
                                       class="setia-input"
                                       placeholder="img_...">
                                <div class="setia-help">
                                    <button type="button" class="setia-help-toggle" data-target="imagine-help">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="currentColor" stroke-width="2"/>
                                            <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        راهنما
                                    </button>
                                    <div class="setia-help-content" id="imagine-help">
                                        <div class="setia-help-steps">
                                            <h4>نحوه دریافت کلید API Imagine Art:</h4>
                                            <ol>
                                                <li>به <a href="https://www.imagine.art/" target="_blank">Imagine Art</a> بروید</li>
                                                <li>ثبت نام کرده و وارد حساب کاربری خود شوید</li>
                                                <li>به بخش API دسترسی پیدا کرده و کلید را دریافت کنید</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Tab -->
                <div class="setia-tab-pane" id="tab-content">
                    <div class="setia-card">
                        <div class="setia-card-header">
                            <div class="setia-card-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </div>
                            <div class="setia-card-title">
                                <h2>تنظیمات محتوا</h2>
                                <p>تنظیمات پیش‌فرض برای تولید محتوا</p>
                            </div>
                        </div>
                        <div class="setia-card-body">
                            <div class="setia-form-row">
                                <div class="setia-form-group">
                                    <label for="default_tone" class="setia-label">لحن پیش‌فرض</label>
                                    <select id="default_tone" name="default_tone" class="setia-select">
                                        <option value="عادی">عادی</option>
                                        <option value="رسمی">رسمی</option>
                                        <option value="دوستانه">دوستانه</option>
                                        <option value="تخصصی">تخصصی</option>
                                        <option value="خلاقانه">خلاقانه</option>
                                    </select>
                                </div>
                                <div class="setia-form-group">
                                    <label for="default_length" class="setia-label">طول پیش‌فرض</label>
                                    <select id="default_length" name="default_length" class="setia-select">
                                        <option value="کوتاه">کوتاه</option>
                                        <option value="متوسط" selected>متوسط</option>
                                        <option value="بلند">بلند</option>
                                        <option value="خیلی بلند">خیلی بلند</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Images Tab -->
                <div class="setia-tab-pane" id="tab-images">
                    <div class="setia-card">
                        <div class="setia-card-header">
                            <div class="setia-card-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                    <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                                    <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </div>
                            <div class="setia-card-title">
                                <h2>تنظیمات تصاویر</h2>
                                <p>تنظیمات پیش‌فرض برای تولید تصاویر</p>
                            </div>
                        </div>
                        <div class="setia-card-body">
                            <div class="setia-form-row">
                                <div class="setia-form-group">
                                    <label for="image_quality" class="setia-label">کیفیت تصویر</label>
                                    <select id="image_quality" name="image_quality" class="setia-select">
                                        <option value="standard" selected>استاندارد</option>
                                        <option value="hd">HD</option>
                                        <option value="ultra">فوق العاده</option>
                                    </select>
                                </div>
                                <div class="setia-form-group">
                                    <label for="image_size" class="setia-label">اندازه تصویر</label>
                                    <select id="image_size" name="image_size" class="setia-select">
                                        <option value="512x512">512x512</option>
                                        <option value="1024x1024" selected>1024x1024</option>
                                        <option value="1024x1792">1024x1792 (عمودی)</option>
                                        <option value="1792x1024">1792x1024 (افقی)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Tab -->
                <div class="setia-tab-pane" id="tab-system">
                    <div class="setia-card">
                        <div class="setia-card-header">
                            <div class="setia-card-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </div>
                            <div class="setia-card-title">
                                <h2>تنظیمات سیستم</h2>
                                <p>تنظیمات عملکرد و کش</p>
                            </div>
                        </div>
                        <div class="setia-card-body">
                            <div class="setia-form-group">
                                <div class="setia-switch-group">
                                    <label class="setia-switch">
                                        <input type="checkbox" id="cache_enabled" name="cache_enabled" checked>
                                        <span class="setia-switch-slider"></span>
                                    </label>
                                    <div class="setia-switch-label">
                                        <strong>فعال‌سازی کش</strong>
                                        <p>ذخیره موقت نتایج برای بهبود سرعت</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="setia-footer">
                <div class="setia-footer-actions">
                    <div class="setia-actions-primary">
                        <button type="submit" name="save_settings" class="setia-button setia-button-primary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            ذخیره تنظیمات
                        </button>
                        <button type="button" id="test-apis" class="setia-button setia-button-secondary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            تست API
                        </button>
                    </div>
                    <div class="setia-actions-secondary">
                        <button type="submit" name="clear_cache" class="setia-button setia-button-warning">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M3 6h18" stroke="currentColor" stroke-width="2"/>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" stroke="currentColor" stroke-width="2"/>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            پاک کردن کش
                        </button>
                        <button type="button" id="reset-settings" class="setia-button setia-button-danger">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <polyline points="1,4 1,10 7,10" stroke="currentColor" stroke-width="2"/>
                                <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            بازنشانی
                        </button>
                    </div>
                </div>
                <div class="setia-footer-info">
                    <p>SETIA Content Generator v2.0 - مولد محتوای هوشمند</p>
                </div>
            </div>
        </form>
    </div>

    <script src="assets/js/settings-enhanced.js"></script>

    <script>
        // Additional debugging
        $(document).ready(function() {
            console.log('Test page ready');

            // Manual test for tab switching
            setTimeout(function() {
                console.log('Testing tab switch to content...');
                if (window.SETIA_Settings && window.SETIA_Settings.switchTab) {
                    window.SETIA_Settings.switchTab('content');
                }
            }, 2000);
        });
    </script>
</body>
</html>
