<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SETIA Final Test</title>
    
    <!-- Load CSS files -->
    <link rel="stylesheet" href="assets/css/admin-settings.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="assets/css/admin.css?v=<?php echo time(); ?>">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f1f1f1;
            font-family: Arial, sans-serif;
        }
        .test-header {
            background: #0073aa;
            color: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
            text-align: center;
        }
        .test-status {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            border: 2px solid #0073aa;
            padding: 15px;
            border-radius: 5px;
            z-index: 9999;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🧪 SETIA Final Functionality Test</h1>
        <p>تست نهایی عملکرد صفحه تنظیمات SETIA</p>
        <p id="test-time"></p>
    </div>

    <div class="test-status" id="test-status">
        <h3>📊 Test Status</h3>
        <div id="status-content">Loading...</div>
    </div>

    <!-- Include the actual SETIA settings content -->
    <div class="setia-settings-wrapper">
        <!-- Header -->
        <div class="setia-header">
            <div class="setia-header-content">
                <h1>⚙️ تنظیمات SETIA</h1>
                <p>مدیریت تنظیمات مولد محتوای هوشمند</p>
            </div>
        </div>

        <!-- Tabs -->
        <div class="setia-tabs">
            <div class="setia-tab-nav">
                <button type="button" class="setia-tab-button active" data-tab="api">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                        <circle cx="9" cy="9" r="2" stroke="currentColor" stroke-width="2"/>
                        <path d="M21 15.5c-.621 0-1-.504-1-1.125" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    API
                </button>
                <button type="button" class="setia-tab-button" data-tab="content">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                        <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                        <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    محتوا
                </button>
                <button type="button" class="setia-tab-button" data-tab="images">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                        <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                        <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    تصاویر
                </button>
                <button type="button" class="setia-tab-button" data-tab="system">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    سیستم
                </button>
            </div>

            <div class="setia-tab-content">
                <!-- API Tab -->
                <div id="tab-api" class="setia-tab-pane active">
                    <h2>🔑 تنظیمات API</h2>
                    <div class="setia-form-group">
                        <label class="setia-form-label">کلید API گوگل جمینی:</label>
                        <input type="text" class="setia-form-input" placeholder="کلید API خود را وارد کنید">
                    </div>
                    <div class="setia-form-group">
                        <label class="setia-form-label">کلید API Imagine Art:</label>
                        <input type="text" class="setia-form-input" placeholder="کلید API خود را وارد کنید">
                    </div>
                </div>

                <!-- Content Tab -->
                <div id="tab-content" class="setia-tab-pane">
                    <h2>📝 تنظیمات محتوا</h2>
                    <div class="setia-form-group">
                        <label class="setia-form-label">تون پیش‌فرض:</label>
                        <select class="setia-form-input">
                            <option>عادی</option>
                            <option>رسمی</option>
                            <option>دوستانه</option>
                        </select>
                    </div>
                </div>

                <!-- Images Tab -->
                <div id="tab-images" class="setia-tab-pane">
                    <h2>🖼️ تنظیمات تصاویر</h2>
                    <div class="setia-form-group">
                        <label class="setia-form-label">کیفیت تصویر:</label>
                        <select class="setia-form-input">
                            <option>استاندارد</option>
                            <option>بالا</option>
                        </select>
                    </div>
                </div>

                <!-- System Tab -->
                <div id="tab-system" class="setia-tab-pane">
                    <h2>⚙️ تنظیمات سیستم</h2>
                    <div class="setia-form-group">
                        <label class="setia-form-label">
                            <input type="checkbox"> فعال‌سازی کش
                        </label>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="setia-footer">
                <div class="setia-actions-primary">
                    <button type="button" class="setia-button setia-button-primary" id="save-settings">
                        💾 ذخیره تنظیمات
                    </button>
                    <button type="button" class="setia-button setia-button-info" id="test-apis">
                        🧪 تست API
                    </button>
                </div>
                <div class="setia-actions-secondary">
                    <button type="button" class="setia-button setia-button-warning" id="clear-cache">
                        🗑️ پاک کردن کش
                    </button>
                    <button type="button" class="setia-button setia-button-danger" id="reset-settings">
                        🔄 بازنشانی
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Load JS files -->
    <script src="assets/js/settings-enhanced.js?v=<?php echo time(); ?>"></script>
    
    <script>
        // Test functionality
        $(document).ready(function() {
            const startTime = new Date();
            $('#test-time').text('شروع تست: ' + startTime.toLocaleTimeString('fa-IR'));
            
            let testResults = [];
            
            // Test 1: Check elements
            const $wrapper = $('.setia-settings-wrapper');
            const $tabs = $('.setia-tab-button');
            const $panes = $('.setia-tab-pane');
            
            testResults.push('✅ Wrapper: ' + $wrapper.length);
            testResults.push('✅ Tabs: ' + $tabs.length);
            testResults.push('✅ Panes: ' + $panes.length);
            
            // Test 2: Check CSS loading
            const wrapperBg = $wrapper.css('background-color');
            testResults.push('✅ CSS loaded: ' + (wrapperBg !== 'rgba(0, 0, 0, 0)' ? 'Yes' : 'No'));
            
            // Test 3: Test tab functionality
            let tabTestPassed = false;
            $tabs.on('click', function() {
                tabTestPassed = true;
                testResults.push('✅ Tab click: Working');
                updateStatus();
            });
            
            // Test 4: Test buttons
            $('#test-apis').on('click', function() {
                testResults.push('✅ Test API button: Working');
                updateStatus();
            });
            
            $('#save-settings').on('click', function() {
                testResults.push('✅ Save button: Working');
                updateStatus();
            });
            
            function updateStatus() {
                $('#status-content').html(testResults.join('<br>'));
            }
            
            updateStatus();
            
            // Auto-test tabs after 2 seconds
            setTimeout(function() {
                console.log('🧪 Auto-testing tabs...');
                $tabs.eq(1).trigger('click');
                
                setTimeout(function() {
                    $tabs.eq(2).trigger('click');
                    
                    setTimeout(function() {
                        $tabs.eq(0).trigger('click');
                        testResults.push('✅ Auto tab test: Complete');
                        updateStatus();
                    }, 500);
                }, 500);
            }, 2000);
        });
    </script>
</body>
</html>
