<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست ساده CSS - SETIA</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f0f0f1;
            direction: rtl;
            text-align: right;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-box {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        
        button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #005a87;
        }
        
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 تست ساده CSS - پلاگین SETIA</h1>
        
        <div class="test-box">
            <h2>📁 تست دسترسی به فایل‌های CSS</h2>
            <button onclick="testCSSFiles()">تست فایل‌های CSS</button>
            <div id="css-results" class="result"></div>
        </div>
        
        <div class="test-box">
            <h2>🌐 تست بارگذاری از مرورگر</h2>
            <p>لینک‌های مستقیم به فایل‌های CSS:</p>
            <div>
                <a href="assets/css/admin.css" target="_blank">admin.css</a> |
                <a href="assets/css/admin-settings.css" target="_blank">admin-settings.css</a> |
                <a href="assets/css/main-page-enhanced.css" target="_blank">main-page-enhanced.css</a> |
                <a href="assets/css/history-advanced.css" target="_blank">history-advanced.css</a>
            </div>
        </div>
        
        <div class="test-box">
            <h2>🔍 بررسی محتوای CSS</h2>
            <button onclick="checkCSSContent()">بررسی محتوا</button>
            <div id="content-results" class="result"></div>
        </div>
        
        <div class="test-box">
            <h2>⚙️ تست تنظیمات WordPress</h2>
            <button onclick="testWordPressSettings()">تست تنظیمات</button>
            <div id="wp-results" class="result"></div>
        </div>
    </div>

    <script>
        async function testCSSFiles() {
            const resultsDiv = document.getElementById('css-results');
            resultsDiv.innerHTML = 'در حال تست...\n';
            
            const cssFiles = [
                'assets/css/admin.css',
                'assets/css/admin-settings.css',
                'assets/css/main-page-enhanced.css',
                'assets/css/history-advanced.css'
            ];
            
            let results = 'نتایج تست فایل‌های CSS:\n\n';
            
            for (const file of cssFiles) {
                try {
                    const response = await fetch(file);
                    const status = response.status;
                    const size = response.headers.get('content-length') || 'نامشخص';
                    
                    if (response.ok) {
                        results += `✅ ${file}: موفق (کد: ${status}, حجم: ${size} بایت)\n`;
                    } else {
                        results += `❌ ${file}: خطا (کد: ${status})\n`;
                    }
                } catch (error) {
                    results += `❌ ${file}: خطای شبکه - ${error.message}\n`;
                }
            }
            
            resultsDiv.innerHTML = results;
            resultsDiv.className = 'result success';
        }
        
        async function checkCSSContent() {
            const resultsDiv = document.getElementById('content-results');
            resultsDiv.innerHTML = 'در حال بررسی محتوا...\n';
            
            try {
                const response = await fetch('assets/css/admin-settings.css');
                if (response.ok) {
                    const content = await response.text();
                    const lines = content.split('\n').length;
                    const hasTestMarker = content.includes('SETIA CSS LOADED');
                    const hasModernStyles = content.includes('glassmorphism') || content.includes('gradient');
                    
                    let results = `بررسی محتوای admin-settings.css:\n\n`;
                    results += `تعداد خطوط: ${lines}\n`;
                    results += `نشانگر تست: ${hasTestMarker ? '✅ موجود' : '❌ موجود نیست'}\n`;
                    results += `استایل‌های مدرن: ${hasModernStyles ? '✅ موجود' : '❌ موجود نیست'}\n`;
                    results += `حجم فایل: ${content.length} کاراکتر\n\n`;
                    
                    // نمایش 10 خط اول
                    const firstLines = content.split('\n').slice(0, 10).join('\n');
                    results += `10 خط اول فایل:\n${firstLines}`;
                    
                    resultsDiv.innerHTML = results;
                    resultsDiv.className = 'result success';
                } else {
                    resultsDiv.innerHTML = `خطا در دسترسی به فایل: ${response.status}`;
                    resultsDiv.className = 'result error';
                }
            } catch (error) {
                resultsDiv.innerHTML = `خطا: ${error.message}`;
                resultsDiv.className = 'result error';
            }
        }
        
        function testWordPressSettings() {
            const resultsDiv = document.getElementById('wp-results');
            
            let results = 'بررسی محیط:\n\n';
            results += `User Agent: ${navigator.userAgent}\n`;
            results += `URL فعلی: ${window.location.href}\n`;
            results += `Protocol: ${window.location.protocol}\n`;
            results += `Host: ${window.location.host}\n`;
            results += `Path: ${window.location.pathname}\n\n`;
            
            // بررسی jQuery
            results += `jQuery: ${typeof jQuery !== 'undefined' ? '✅ بارگذاری شده' : '❌ بارگذاری نشده'}\n`;
            
            // بررسی CSS links موجود
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            results += `تعداد CSS Links: ${cssLinks.length}\n`;
            
            // بررسی فایل‌های SETIA
            const setiaLinks = Array.from(cssLinks).filter(link => link.href.includes('setia'));
            results += `CSS های SETIA: ${setiaLinks.length}\n`;
            
            if (setiaLinks.length > 0) {
                results += '\nCSS های SETIA یافت شده:\n';
                setiaLinks.forEach(link => {
                    results += `- ${link.href}\n`;
                });
            }
            
            resultsDiv.innerHTML = results;
            resultsDiv.className = 'result success';
        }
        
        // تست خودکار هنگام بارگذاری صفحه
        window.addEventListener('load', function() {
            console.log('صفحه تست CSS بارگذاری شد');
            testWordPressSettings();
        });
    </script>
</body>
</html>
