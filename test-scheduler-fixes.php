<?php
/**
 * فایل تست برای بررسی رفع مشکلات زمانبندی
 */

// بارگذاری وردپرس
$wp_load_paths = [
    '../../../wp-load.php',
    '../../../../wp-load.php',
    '../wp-load.php'
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('خطا: فایل wp-load.php یافت نشد.');
}

// بررسی دسترسی ادمین
if (!current_user_can('manage_options')) {
    wp_die('شما اجازه دسترسی به این صفحه را ندارید.');
}

echo '<h1>🔧 تست رفع مشکلات زمانبندی SETIA</h1>';
echo '<style>body { font-family: <PERSON><PERSON><PERSON>, <PERSON>l, sans-serif; direction: rtl; } .success { color: green; } .error { color: red; } .info { color: blue; }</style>';

// 1. بررسی بارگذاری کلاس‌ها
echo '<h2>1️⃣ بررسی بارگذاری کلاس‌ها</h2>';

if (class_exists('SETIA_Content_Generator')) {
    echo '<p class="success">✅ کلاس SETIA_Content_Generator بارگذاری شده</p>';
} else {
    echo '<p class="error">❌ کلاس SETIA_Content_Generator بارگذاری نشده</p>';
}

if (class_exists('SETIA_Scheduler')) {
    echo '<p class="success">✅ کلاس SETIA_Scheduler بارگذاری شده</p>';
} else {
    echo '<p class="error">❌ کلاس SETIA_Scheduler بارگذاری نشده</p>';
}

// 2. بررسی ثبت AJAX handlers
echo '<h2>2️⃣ بررسی ثبت AJAX handlers</h2>';

global $wp_filter;

$ajax_actions = [
    'wp_ajax_setia_save_schedule' => 'ذخیره زمانبندی',
    'wp_ajax_setia_delete_schedule' => 'حذف زمانبندی',
    'wp_ajax_setia_get_schedules' => 'دریافت زمانبندی‌ها',
    'wp_ajax_setia_save_scheduler_settings' => 'ذخیره تنظیمات زمانبندی',
    'wp_ajax_setia_test_scheduler' => 'تست زمانبندی',
    'wp_ajax_setia_run_scheduler_now' => 'اجرای فوری زمانبندی',
    'wp_ajax_setia_clear_scheduler_logs' => 'پاک کردن لاگ‌ها',
    'wp_ajax_setia_get_scheduler_logs' => 'دریافت لاگ‌ها'
];

foreach ($ajax_actions as $action => $description) {
    if (isset($wp_filter[$action])) {
        echo '<p class="success">✅ ' . $description . ' (' . $action . ')</p>';
    } else {
        echo '<p class="error">❌ ' . $description . ' (' . $action . ')</p>';
    }
}

// 3. بررسی وجود جداول دیتابیس
echo '<h2>3️⃣ بررسی وجود جداول دیتابیس</h2>';

global $wpdb;

$tables = [
    $wpdb->prefix . 'setia_generated_content' => 'جدول تاریخچه محتوا',
    $wpdb->prefix . 'setia_content_schedules' => 'جدول زمانبندی‌ها',
    $wpdb->prefix . 'setia_scheduler_logs' => 'جدول لاگ زمانبندی'
];

foreach ($tables as $table_name => $description) {
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    if ($table_exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        echo '<p class="success">✅ ' . $description . ' (' . $table_name . ') - ' . $count . ' رکورد</p>';
    } else {
        echo '<p class="error">❌ ' . $description . ' (' . $table_name . ') وجود ندارد</p>';
    }
}

// 4. بررسی کرون‌های سفارشی
echo '<h2>4️⃣ بررسی کرون‌های سفارشی</h2>';

$cron_schedules = wp_get_schedules();
$custom_schedules = ['minutely', 'every5minutes', 'every15minutes', 'every30minutes'];

foreach ($custom_schedules as $schedule) {
    if (isset($cron_schedules[$schedule])) {
        echo '<p class="success">✅ کرون ' . $schedule . ' ثبت شده (' . $cron_schedules[$schedule]['display'] . ')</p>';
    } else {
        echo '<p class="error">❌ کرون ' . $schedule . ' ثبت نشده</p>';
    }
}

// 5. بررسی منوهای ادمین
echo '<h2>5️⃣ بررسی منوهای ادمین</h2>';

global $submenu;

if (isset($submenu['setia-content-generator'])) {
    echo '<p class="info">📋 منوهای موجود:</p>';
    echo '<ul>';
    foreach ($submenu['setia-content-generator'] as $item) {
        echo '<li>' . $item[0] . ' (' . $item[2] . ')</li>';
    }
    echo '</ul>';
    
    // بررسی وجود منوی زمانبندی
    $scheduler_menu_exists = false;
    foreach ($submenu['setia-content-generator'] as $item) {
        if ($item[2] === 'setia-scheduler') {
            $scheduler_menu_exists = true;
            break;
        }
    }
    
    if ($scheduler_menu_exists) {
        echo '<p class="success">✅ منوی زمانبندی موجود است</p>';
    } else {
        echo '<p class="error">❌ منوی زمانبندی موجود نیست</p>';
    }
} else {
    echo '<p class="error">❌ منوی اصلی پلاگین یافت نشد</p>';
}

// 6. بررسی فایل‌های template
echo '<h2>6️⃣ بررسی فایل‌های template</h2>';

$template_files = [
    'templates/scheduler-page.php' => 'صفحه زمانبندی',
    'templates/cron-settings-page.php' => 'صفحه تنظیمات کرون',
    'templates/internal-cron-page.php' => 'صفحه کرون داخلی'
];

foreach ($template_files as $file => $description) {
    $full_path = plugin_dir_path(__FILE__) . $file;
    if (file_exists($full_path)) {
        echo '<p class="success">✅ ' . $description . ' (' . $file . ')</p>';
    } else {
        echo '<p class="error">❌ ' . $description . ' (' . $file . ') وجود ندارد</p>';
    }
}

// 7. تست ساده AJAX
echo '<h2>7️⃣ تست ساده AJAX</h2>';

if (class_exists('SETIA_Scheduler')) {
    echo '<p class="info">🧪 تست ایجاد نمونه کلاس زمانبندی...</p>';
    
    try {
        // ایجاد نمونه کلاس اصلی
        if (class_exists('SETIA_Content_Generator')) {
            $setia = new SETIA_Content_Generator();
            echo '<p class="success">✅ نمونه کلاس اصلی ایجاد شد</p>';
            
            // ایجاد نمونه کلاس زمانبندی
            $scheduler = new SETIA_Scheduler($setia);
            echo '<p class="success">✅ نمونه کلاس زمانبندی ایجاد شد</p>';
            
            // بررسی متدهای کلیدی
            $methods = ['save_schedule', 'delete_schedule', 'get_schedules'];
            foreach ($methods as $method) {
                if (method_exists($scheduler, $method)) {
                    echo '<p class="success">✅ متد ' . $method . ' موجود است</p>';
                } else {
                    echo '<p class="error">❌ متد ' . $method . ' موجود نیست</p>';
                }
            }
            
        } else {
            echo '<p class="error">❌ کلاس اصلی یافت نشد</p>';
        }
        
    } catch (Exception $e) {
        echo '<p class="error">❌ خطا در ایجاد نمونه: ' . $e->getMessage() . '</p>';
    }
} else {
    echo '<p class="error">❌ کلاس زمانبندی یافت نشد</p>';
}

echo '<h2>🎯 خلاصه نتایج</h2>';
echo '<p class="info">اگر تمام موارد بالا سبز (✅) هستند، سیستم زمانبندی باید کاملاً کار کند.</p>';
echo '<p class="info">در صورت وجود مشکل، لطفاً موارد قرمز (❌) را بررسی کنید.</p>';

?>
