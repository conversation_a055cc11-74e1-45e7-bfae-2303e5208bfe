<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست متغیرهای CSS - SETIA</title>
    
    <!-- بارگذاری CSS های SETIA -->
    <link rel="stylesheet" href="assets/css/admin.css">
    <link rel="stylesheet" href="assets/css/admin-settings.css">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--setia-gray-50, #f9fafb);
            direction: rtl;
            text-align: right;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--setia-white, white);
            padding: 30px;
            border-radius: var(--setia-radius-lg, 12px);
            box-shadow: var(--setia-shadow-lg, 0 10px 15px rgba(0, 0, 0, 0.1));
        }
        
        .variable-test {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid var(--setia-gray-200, #e5e7eb);
            border-radius: var(--setia-radius, 8px);
            background: var(--setia-gray-50, #f9fafb);
        }
        
        .color-box {
            width: 50px;
            height: 50px;
            border-radius: var(--setia-radius, 8px);
            display: inline-block;
            margin: 5px;
            border: 2px solid var(--setia-gray-300, #d1d5db);
        }
        
        .primary-color { background: var(--setia-primary, #6244EC); }
        .secondary-color { background: var(--setia-secondary, #428df5); }
        .success-color { background: var(--setia-success, #10b981); }
        .error-color { background: var(--setia-error, #f43f5e); }
        .warning-color { background: var(--setia-warning, #f59e0b); }
        
        .spacing-test {
            padding: var(--setia-space-4, 1rem);
            margin: var(--setia-space-2, 0.5rem);
            background: var(--setia-primary, #6244EC);
            color: var(--setia-white, white);
            border-radius: var(--setia-radius, 8px);
            display: inline-block;
        }
        
        .text-size-test {
            margin: 10px 0;
        }
        
        .text-xs { font-size: var(--setia-text-xs, 0.75rem); }
        .text-sm { font-size: var(--setia-text-sm, 0.875rem); }
        .text-base { font-size: var(--setia-text-base, 1rem); }
        .text-lg { font-size: var(--setia-text-lg, 1.125rem); }
        .text-xl { font-size: var(--setia-text-xl, 1.25rem); }
        .text-2xl { font-size: var(--setia-text-2xl, 1.5rem); }
        
        .gradient-test {
            background: var(--setia-gradient-primary, linear-gradient(135deg, #6244EC 0%, #428df5 100%));
            color: var(--setia-white, white);
            padding: var(--setia-space-6, 1.5rem);
            border-radius: var(--setia-radius-lg, 12px);
            margin: 20px 0;
            text-align: center;
        }
        
        .shadow-test {
            padding: var(--setia-space-4, 1rem);
            margin: var(--setia-space-4, 1rem);
            background: var(--setia-white, white);
            border-radius: var(--setia-radius, 8px);
            display: inline-block;
        }
        
        .shadow-sm { box-shadow: var(--setia-shadow-sm, 0 1px 2px rgba(0, 0, 0, 0.05)); }
        .shadow-md { box-shadow: var(--setia-shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1)); }
        .shadow-lg { box-shadow: var(--setia-shadow-lg, 0 10px 15px rgba(0, 0, 0, 0.1)); }
        .shadow-xl { box-shadow: var(--setia-shadow-xl, 0 20px 25px rgba(0, 0, 0, 0.1)); }
        
        .transition-test {
            padding: var(--setia-space-4, 1rem);
            background: var(--setia-primary, #6244EC);
            color: var(--setia-white, white);
            border-radius: var(--setia-radius, 8px);
            transition: var(--setia-transition, all 0.2s ease);
            cursor: pointer;
            display: inline-block;
            margin: 10px;
        }
        
        .transition-test:hover {
            transform: translateY(-2px);
            box-shadow: var(--setia-shadow-lg, 0 10px 15px rgba(0, 0, 0, 0.1));
        }
        
        .results {
            background: var(--setia-gray-50, #f9fafb);
            border: 1px solid var(--setia-gray-200, #e5e7eb);
            border-radius: var(--setia-radius, 8px);
            padding: var(--setia-space-4, 1rem);
            margin: var(--setia-space-4, 1rem) 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        button {
            background: var(--setia-primary, #6244EC);
            color: var(--setia-white, white);
            border: none;
            padding: var(--setia-space-3, 0.75rem) var(--setia-space-6, 1.5rem);
            border-radius: var(--setia-radius, 8px);
            cursor: pointer;
            font-size: var(--setia-text-base, 1rem);
            margin: var(--setia-space-2, 0.5rem);
            transition: var(--setia-transition, all 0.2s ease);
        }
        
        button:hover {
            background: var(--setia-primary-600, #5234DB);
            transform: translateY(-1px);
            box-shadow: var(--setia-shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 تست متغیرهای CSS - پلاگین SETIA</h1>
        
        <div class="variable-test">
            <h2>🎨 تست رنگ‌ها</h2>
            <div>
                <div class="color-box primary-color" title="Primary Color"></div>
                <div class="color-box secondary-color" title="Secondary Color"></div>
                <div class="color-box success-color" title="Success Color"></div>
                <div class="color-box error-color" title="Error Color"></div>
                <div class="color-box warning-color" title="Warning Color"></div>
            </div>
            <p>اگر رنگ‌ها درست نمایش داده می‌شوند، متغیرهای رنگ کار می‌کنند.</p>
        </div>
        
        <div class="variable-test">
            <h2>📏 تست فاصله‌ها</h2>
            <div class="spacing-test">فاصله کوچک</div>
            <div class="spacing-test" style="padding: var(--setia-space-6, 1.5rem);">فاصله متوسط</div>
            <div class="spacing-test" style="padding: var(--setia-space-8, 2rem);">فاصله بزرگ</div>
        </div>
        
        <div class="variable-test">
            <h2>📝 تست اندازه متن</h2>
            <div class="text-size-test text-xs">متن خیلی کوچک (xs)</div>
            <div class="text-size-test text-sm">متن کوچک (sm)</div>
            <div class="text-size-test text-base">متن معمولی (base)</div>
            <div class="text-size-test text-lg">متن بزرگ (lg)</div>
            <div class="text-size-test text-xl">متن خیلی بزرگ (xl)</div>
            <div class="text-size-test text-2xl">متن عظیم (2xl)</div>
        </div>
        
        <div class="variable-test">
            <h2>🌈 تست گرادیان</h2>
            <div class="gradient-test">
                این بخش باید با گرادیان زیبا نمایش داده شود
            </div>
        </div>
        
        <div class="variable-test">
            <h2>🔳 تست سایه</h2>
            <div class="shadow-test shadow-sm">سایه کوچک</div>
            <div class="shadow-test shadow-md">سایه متوسط</div>
            <div class="shadow-test shadow-lg">سایه بزرگ</div>
            <div class="shadow-test shadow-xl">سایه خیلی بزرگ</div>
        </div>
        
        <div class="variable-test">
            <h2>⚡ تست انتقال</h2>
            <div class="transition-test">روی من هاور کنید</div>
            <div class="transition-test">من هم انیمیشن دارم</div>
        </div>
        
        <div class="variable-test">
            <h2>🧪 تست‌های JavaScript</h2>
            <button onclick="testCSSVariables()">تست متغیرهای CSS</button>
            <button onclick="testComputedStyles()">تست Computed Styles</button>
            <button onclick="testCSSSupport()">تست پشتیبانی CSS</button>
            
            <div class="results" id="testResults"></div>
        </div>
        
        <!-- تست استایل‌های SETIA واقعی -->
        <div class="setia-section">
            <div class="setia-section-header">
                <div class="setia-section-icon">🎯</div>
                <div class="setia-section-title">
                    <h2>تست استایل‌های SETIA واقعی</h2>
                    <p>این بخش از استایل‌های واقعی admin-settings.css استفاده می‌کند</p>
                </div>
            </div>
            <div class="setia-section-content">
                <div class="setia-form-group">
                    <label class="setia-label">
                        <span class="label-icon">🔑</span>
                        نمونه فیلد ورودی
                        <span class="required-indicator">*</span>
                    </label>
                    <input type="text" class="setia-input" placeholder="متن نمونه وارد کنید">
                    <p class="setia-field-description">این یک فیلد نمونه برای تست استایل‌ها است.</p>
                </div>
                
                <div class="setia-form-group">
                    <label class="setia-label">
                        <span class="label-icon">📋</span>
                        نمونه انتخاب
                    </label>
                    <select class="setia-select">
                        <option>گزینه اول</option>
                        <option>گزینه دوم</option>
                        <option>گزینه سوم</option>
                    </select>
                </div>
                
                <button class="setia-button setia-button-primary setia-button-large">
                    <span class="button-icon">💾</span>
                    ذخیره تنظیمات
                </button>
            </div>
        </div>
    </div>

    <script>
        function testCSSVariables() {
            const results = document.getElementById('testResults');
            results.innerHTML = 'تست متغیرهای CSS...\n\n';
            
            const variables = [
                '--setia-primary',
                '--setia-secondary',
                '--setia-success',
                '--setia-error',
                '--setia-warning',
                '--setia-white',
                '--setia-gray-50',
                '--setia-gray-200',
                '--setia-space-4',
                '--setia-text-base',
                '--setia-radius',
                '--setia-shadow',
                '--setia-transition'
            ];
            
            const rootStyles = getComputedStyle(document.documentElement);
            
            variables.forEach(variable => {
                const value = rootStyles.getPropertyValue(variable).trim();
                const status = value ? '✅' : '❌';
                results.innerHTML += `${status} ${variable}: ${value || 'تعریف نشده'}\n`;
            });
        }
        
        function testComputedStyles() {
            const results = document.getElementById('testResults');
            results.innerHTML = 'تست Computed Styles...\n\n';
            
            const testElement = document.querySelector('.setia-section');
            if (testElement) {
                const styles = getComputedStyle(testElement);
                results.innerHTML += `Background: ${styles.backgroundColor}\n`;
                results.innerHTML += `Border Radius: ${styles.borderRadius}\n`;
                results.innerHTML += `Box Shadow: ${styles.boxShadow}\n`;
                results.innerHTML += `Margin Bottom: ${styles.marginBottom}\n`;
                results.innerHTML += `Padding: ${styles.padding}\n`;
            } else {
                results.innerHTML += '❌ عنصر .setia-section یافت نشد\n';
            }
            
            // تست فیلد ورودی
            const inputElement = document.querySelector('.setia-input');
            if (inputElement) {
                const inputStyles = getComputedStyle(inputElement);
                results.innerHTML += `\nInput Styles:\n`;
                results.innerHTML += `Border: ${inputStyles.border}\n`;
                results.innerHTML += `Border Radius: ${inputStyles.borderRadius}\n`;
                results.innerHTML += `Padding: ${inputStyles.padding}\n`;
                results.innerHTML += `Font Size: ${inputStyles.fontSize}\n`;
            }
        }
        
        function testCSSSupport() {
            const results = document.getElementById('testResults');
            results.innerHTML = 'تست پشتیبانی CSS...\n\n';
            
            const features = [
                { name: 'CSS Variables', test: () => CSS.supports('color', 'var(--test)') },
                { name: 'CSS Grid', test: () => CSS.supports('display', 'grid') },
                { name: 'CSS Flexbox', test: () => CSS.supports('display', 'flex') },
                { name: 'CSS Backdrop Filter', test: () => CSS.supports('backdrop-filter', 'blur(10px)') },
                { name: 'CSS Transforms', test: () => CSS.supports('transform', 'translateY(-2px)') },
                { name: 'CSS Transitions', test: () => CSS.supports('transition', 'all 0.2s ease') },
                { name: 'CSS Box Shadow', test: () => CSS.supports('box-shadow', '0 4px 6px rgba(0, 0, 0, 0.1)') }
            ];
            
            features.forEach(feature => {
                const supported = feature.test();
                const status = supported ? '✅' : '❌';
                results.innerHTML += `${status} ${feature.name}: ${supported ? 'پشتیبانی می‌شود' : 'پشتیبانی نمی‌شود'}\n`;
            });
            
            // تست CSS links
            results.innerHTML += '\n--- CSS Links ---\n';
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            cssLinks.forEach((link, index) => {
                const href = link.href;
                const isSetia = href.includes('setia') || href.includes('admin');
                results.innerHTML += `${index + 1}. ${href.split('/').pop()} ${isSetia ? '(SETIA)' : ''}\n`;
            });
        }
        
        // تست خودکار هنگام بارگذاری
        window.addEventListener('load', function() {
            console.log('🚀 صفحه تست متغیرهای CSS بارگذاری شد');
            setTimeout(testCSSVariables, 1000);
        });
    </script>
</body>
</html>
