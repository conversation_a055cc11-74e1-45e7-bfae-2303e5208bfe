<?php
/**
 * SETIA Cache Clearing Script
 * Run this file to clear all caches and force reload
 */

// WordPress environment
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo '<h1>SETIA Cache Clearing</h1>';

// Clear WordPress cache
wp_cache_flush();
echo '<p>✅ WordPress cache cleared</p>';

// Clear transients
global $wpdb;
$deleted_transients = $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_setia_%'");
$deleted_timeouts = $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_setia_%'");
echo "<p>✅ Deleted {$deleted_transients} transients and {$deleted_timeouts} timeouts</p>";

// Update asset version
$new_version = time();
update_option('setia_asset_version', $new_version);
echo "<p>✅ Asset version updated to: {$new_version}</p>";

// Clear opcache if available
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo '<p>✅ OPcache cleared</p>';
}

echo '<p><strong>Cache clearing completed!</strong></p>';
echo '<p><a href="' . admin_url('admin.php?page=setia-settings') . '">Go to SETIA Settings</a></p>';
?>
