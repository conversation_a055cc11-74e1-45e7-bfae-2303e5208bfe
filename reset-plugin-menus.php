<?php
/**
 * اسکریپت بازنشانی منوهای پلاگین SETIA
 * این فایل برای پاک کردن کش و بازنشانی منوها استفاده می‌شود
 */

// جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    // اگر WordPress لود نشده، آن را لود کنیم
    require_once('../../../wp-config.php');
}

// بررسی دسترسی ادمین
if (!current_user_can('manage_options')) {
    wp_die('شما دسترسی لازم برای این عملیات را ندارید.');
}

?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بازنشانی منوهای پلاگین SETIA</title>
    <style>
        body {
            font-family: 'Tahoma', Arial, sans-serif;
            margin: 20px;
            background: #f1f1f1;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .action-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .success { border-right: 4px solid #28a745; background: #d4edda; }
        .error { border-right: 4px solid #dc3545; background: #f8d7da; }
        .warning { border-right: 4px solid #ffc107; background: #fff3cd; }
        .info { border-right: 4px solid #17a2b8; background: #d1ecf1; }
        
        .btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        
        .step {
            margin: 15px 0;
            padding: 15px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .step h4 {
            margin-top: 0;
            color: #333;
        }
        .step-number {
            background: #007cba;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 بازنشانی منوهای پلاگین SETIA</h1>
            <p>پاک‌سازی کش و رفع مشکل منوهای تکراری</p>
        </div>

        <?php
        $action = isset($_GET['action']) ? $_GET['action'] : '';
        $step = isset($_GET['step']) ? intval($_GET['step']) : 0;
        
        if ($action === 'reset' && $step > 0) {
            echo '<div class="action-card info">';
            echo '<h3>🔄 در حال اجرای مرحله ' . $step . '...</h3>';
            
            switch ($step) {
                case 1:
                    // پاک کردن کش WordPress
                    if (function_exists('wp_cache_flush')) {
                        wp_cache_flush();
                        echo '<p>✅ کش WordPress پاک شد</p>';
                    }
                    
                    // پاک کردن transients
                    global $wpdb;
                    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%'");
                    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_site_transient_%'");
                    echo '<p>✅ Transients پاک شدند</p>';
                    
                    // پاک کردن کش منوها
                    delete_option('setia_menu_cache');
                    echo '<p>✅ کش منوهای SETIA پاک شد</p>';
                    break;
                    
                case 2:
                    // غیرفعال کردن پلاگین
                    $plugin_file = 'setia-content-generator/setia-content-generator.php';
                    if (is_plugin_active($plugin_file)) {
                        deactivate_plugins($plugin_file);
                        echo '<p>✅ پلاگین SETIA غیرفعال شد</p>';
                    } else {
                        echo '<p>⚠️ پلاگین قبلاً غیرفعال بود</p>';
                    }
                    break;
                    
                case 3:
                    // فعال کردن مجدد پلاگین
                    $plugin_file = 'setia-content-generator/setia-content-generator.php';
                    if (!is_plugin_active($plugin_file)) {
                        activate_plugin($plugin_file);
                        echo '<p>✅ پلاگین SETIA مجدداً فعال شد</p>';
                    } else {
                        echo '<p>⚠️ پلاگین قبلاً فعال بود</p>';
                    }
                    break;
                    
                case 4:
                    // بررسی نهایی منوها
                    global $menu, $submenu;
                    $setia_submenus = isset($submenu['setia-content-generator']) ? $submenu['setia-content-generator'] : array();
                    $total_submenus = count($setia_submenus);
                    $unique_slugs = array_unique(array_column($setia_submenus, 2));
                    $duplicate_count = $total_submenus - count($unique_slugs);
                    
                    echo '<p><strong>📊 نتایج بررسی:</strong></p>';
                    echo '<p>• تعداد زیرمنوها: ' . $total_submenus . '</p>';
                    echo '<p>• منوهای یکتا: ' . count($unique_slugs) . '</p>';
                    echo '<p>• منوهای تکراری: ' . $duplicate_count . '</p>';
                    
                    if ($duplicate_count === 0) {
                        echo '<p style="color: #28a745; font-weight: bold;">✅ مشکل تکرار منوها رفع شد!</p>';
                    } else {
                        echo '<p style="color: #dc3545; font-weight: bold;">❌ هنوز ' . $duplicate_count . ' منوی تکراری وجود دارد</p>';
                    }
                    break;
            }
            
            echo '</div>';
            
            // نمایش دکمه مرحله بعد
            if ($step < 4) {
                $next_step = $step + 1;
                echo '<a href="?action=reset&step=' . $next_step . '" class="btn btn-success">ادامه به مرحله ' . $next_step . '</a>';
            } else {
                echo '<a href="' . admin_url('admin.php?page=setia-content-generator') . '" class="btn btn-success">مشاهده منوهای SETIA</a>';
            }
        } else {
            // نمایش مراحل
            ?>
            <div class="action-card warning">
                <h3>⚠️ هشدار مهم</h3>
                <p>این عملیات منوهای پلاگین را بازنشانی می‌کند. لطفاً قبل از ادامه اطمینان حاصل کنید که:</p>
                <ul>
                    <li>از فایل‌های پلاگین نسخه پشتیبان تهیه کرده‌اید</li>
                    <li>هیچ کاربر دیگری در حال استفاده از پنل ادمین نیست</li>
                    <li>دسترسی مدیریت سایت را دارید</li>
                </ul>
            </div>

            <h3>📋 مراحل بازنشانی:</h3>
            
            <div class="step">
                <h4><span class="step-number">1</span>پاک‌سازی کش</h4>
                <p>پاک کردن کش WordPress، transients و کش منوهای SETIA</p>
            </div>
            
            <div class="step">
                <h4><span class="step-number">2</span>غیرفعال کردن پلاگین</h4>
                <p>غیرفعال کردن موقت پلاگین SETIA برای بازنشانی منوها</p>
            </div>
            
            <div class="step">
                <h4><span class="step-number">3</span>فعال‌سازی مجدد</h4>
                <p>فعال کردن مجدد پلاگین با منوهای به‌روزرسانی شده</p>
            </div>
            
            <div class="step">
                <h4><span class="step-number">4</span>بررسی نهایی</h4>
                <p>تأیید رفع مشکل تکرار منوها و نمایش گزارش نهایی</p>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <a href="?action=reset&step=1" class="btn btn-danger">🚀 شروع بازنشانی</a>
                <a href="<?php echo admin_url('plugins.php'); ?>" class="btn">بازگشت به پلاگین‌ها</a>
            </div>
            <?php
        }
        ?>

        <div style="text-align: center; margin-top: 20px; color: #666;">
            <p>📅 تاریخ: <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>
</body>
</html>
