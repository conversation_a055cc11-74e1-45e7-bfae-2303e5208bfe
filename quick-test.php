<?php
/**
 * تست سریع وضعیت پلاگین
 */

// بارگذاری وردپرس
require_once('../../../wp-load.php');

// بررسی دسترسی
if (!current_user_can('manage_options')) {
    die('دسترسی غیرمجاز');
}

echo '<h1>تست سریع پلاگین SETIA</h1>';
echo '<style>body { font-family: Tahoma; direction: rtl; }</style>';

// بررسی فعال بودن پلاگین
if (is_plugin_active('setia-content-generator/setia-content-generator.php')) {
    echo '<p style="color: green;">✅ پلاگین فعال است</p>';
} else {
    echo '<p style="color: red;">❌ پلاگین غیرفعال است</p>';
}

// بررسی کلاس‌ها
if (class_exists('SETIA_Content_Generator')) {
    echo '<p style="color: green;">✅ کلاس اصلی موجود است</p>';
} else {
    echo '<p style="color: red;">❌ کلاس اصلی موجود نیست</p>';
}

if (class_exists('SETIA_Scheduler')) {
    echo '<p style="color: green;">✅ کلاس زمانبندی موجود است</p>';
} else {
    echo '<p style="color: red;">❌ کلاس زمانبندی موجود نیست</p>';
}

// بررسی جداول
global $wpdb;
$table_name = $wpdb->prefix . 'setia_content_schedules';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    echo '<p style="color: green;">✅ جدول زمانبندی موجود است</p>';
} else {
    echo '<p style="color: red;">❌ جدول زمانبندی موجود نیست</p>';
}

// بررسی منو
global $submenu;
if (isset($submenu['setia-content-generator'])) {
    echo '<p style="color: green;">✅ منوی پلاگین موجود است</p>';
    
    $scheduler_exists = false;
    foreach ($submenu['setia-content-generator'] as $item) {
        if ($item[2] === 'setia-scheduler') {
            $scheduler_exists = true;
            break;
        }
    }
    
    if ($scheduler_exists) {
        echo '<p style="color: green;">✅ منوی زمانبندی موجود است</p>';
    } else {
        echo '<p style="color: red;">❌ منوی زمانبندی موجود نیست</p>';
    }
} else {
    echo '<p style="color: red;">❌ منوی پلاگین موجود نیست</p>';
}

echo '<hr>';
echo '<p><a href="/WP/wp-admin/admin.php?page=setia-scheduler">🔗 رفتن به صفحه زمانبندی</a></p>';
echo '<p><a href="/WP/wp-admin/plugins.php">🔗 رفتن به صفحه پلاگین‌ها</a></p>';

?>
