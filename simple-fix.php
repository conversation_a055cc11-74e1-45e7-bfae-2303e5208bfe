<?php
/**
 * Simple Fix for SETIA Plugin
 * رفع ساده مشکلات پلاگین SETIA
 */

// بارگذاری WordPress
if (!defined('ABSPATH')) {
    $wp_paths = [
        __DIR__ . '/../../../wp-load.php',
        __DIR__ . '/../../../../wp-load.php'
    ];
    
    foreach ($wp_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            break;
        }
    }
}

if (!defined('ABSPATH')) {
    die('WordPress یافت نشد!');
}

// بررسی دسترسی
if (!current_user_can('manage_options')) {
    die('دسترسی غیرمجاز!');
}

echo "<!DOCTYPE html><html dir='rtl'><head><meta charset='UTF-8'><title>رفع مشکلات SETIA</title>";
echo "<style>body{font-family:<PERSON><PERSON><PERSON>;direction:rtl;margin:20px;} .ok{color:green;} .error{color:red;} .info{color:blue;}</style></head><body>";

echo "<h1>🔧 رفع سریع مشکلات SETIA</h1>";

// 1. ایجاد جداول
echo "<h2>ایجاد جداول دیتابیس...</h2>";
global $wpdb;

$queries = [
    "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}setia_generated_content (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        post_id mediumint(9) DEFAULT NULL,
        topic varchar(255) NOT NULL,
        keywords text NOT NULL,
        tone varchar(50) NOT NULL,
        category varchar(100) NOT NULL,
        length varchar(50) NOT NULL,
        generated_text longtext NOT NULL,
        generated_image_url varchar(255) DEFAULT NULL,
        seo_meta text DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) {$wpdb->get_charset_collate()};",
    
    "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}setia_content_schedules (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        title varchar(255) NOT NULL,
        topic varchar(255) NOT NULL,
        keywords text,
        category_id mediumint(9) DEFAULT NULL,
        tone varchar(50) DEFAULT 'عادی',
        length varchar(50) DEFAULT 'متوسط',
        frequency varchar(50) NOT NULL,
        status tinyint(1) DEFAULT 1,
        daily_limit int(11) DEFAULT 1,
        generate_image tinyint(1) DEFAULT 1,
        last_run datetime DEFAULT NULL,
        next_run datetime DEFAULT NULL,
        generated_count int(11) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) {$wpdb->get_charset_collate()};",
    
    "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}setia_scheduler_logs (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        schedule_id mediumint(9) DEFAULT NULL,
        message text NOT NULL,
        type varchar(20) DEFAULT 'info',
        post_id mediumint(9) DEFAULT NULL,
        execution_time varchar(50) DEFAULT NULL,
        memory_usage varchar(50) DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY schedule_id (schedule_id)
    ) {$wpdb->get_charset_collate()};"
];

foreach ($queries as $i => $query) {
    $result = $wpdb->query($query);
    if ($result !== false) {
        echo "<p class='ok'>✅ جدول " . ($i + 1) . " ایجاد شد</p>";
    } else {
        echo "<p class='error'>❌ خطا در ایجاد جدول " . ($i + 1) . "</p>";
    }
}

// 2. تنظیمات پیش‌فرض
echo "<h2>تنظیم گزینه‌های پیش‌فرض...</h2>";
$settings = [
    'gemini_api_key' => '',
    'gemma_api_key' => '',
    'imagine_art_api_key' => '',
    'default_tone' => 'عادی',
    'default_length' => 'متوسط',
    'enable_seo' => 'yes',
    'enable_image_generation' => 'yes',
    'default_image_style' => 'realistic',
    'default_aspect_ratio' => '16:9',
    'internal_cron_interval' => 15
];

update_option('setia_settings', $settings);
echo "<p class='ok'>✅ تنظیمات ذخیره شدند</p>";

// 3. پاک کردن کش
echo "<h2>پاک کردن کش...</h2>";
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "<p class='ok'>✅ کش پاک شد</p>";
}

$time = time();
update_option('setia_asset_version', $time);
update_option('setia_css_version', $time);
update_option('setia_js_version', $time);
echo "<p class='ok'>✅ نسخه‌ها بروزرسانی شدند</p>";

// 4. بررسی نهایی
echo "<h2>بررسی نهایی...</h2>";

$tables = [
    $wpdb->prefix . 'setia_generated_content',
    $wpdb->prefix . 'setia_content_schedules',
    $wpdb->prefix . 'setia_scheduler_logs'
];

$all_exist = true;
foreach ($tables as $table) {
    if ($wpdb->get_var("SHOW TABLES LIKE '$table'")) {
        echo "<p class='ok'>✅ جدول $table موجود است</p>";
    } else {
        echo "<p class='error'>❌ جدول $table موجود نیست</p>";
        $all_exist = false;
    }
}

if ($all_exist) {
    echo "<h2 class='ok'>🎉 همه چیز آماده است!</h2>";
} else {
    echo "<h2 class='error'>⚠️ برخی مشکلات باقی مانده</h2>";
}

echo "<p><a href='" . admin_url('admin.php?page=setia-scheduler') . "' style='background:#0073aa;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>بازگشت به زمانبندی</a></p>";

echo "</body></html>";
?>
