<?php
/**
 * بررسی‌کننده منوهای ادمین پلاگین SETIA
 * این فایل در پنل ادمین WordPress اجرا می‌شود
 */

// جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// بررسی دسترسی ادمین
if (!current_user_can('manage_options')) {
    wp_die('شما دسترسی لازم برای این عملیات را ندارید.');
}

// اضافه کردن این صفحه به منوی ادمین برای تست
add_action('admin_menu', function() {
    add_submenu_page(
        'tools.php',
        'بررسی منوهای SETIA',
        'بررسی منوهای SETIA',
        'manage_options',
        'setia-menu-checker',
        'setia_menu_checker_page'
    );
});

function setia_menu_checker_page() {
    global $menu, $submenu;
    
    // دریافت منوهای SETIA
    $setia_menu_found = false;
    $setia_menu_data = null;
    $setia_submenus = array();
    
    // جستجو در منوی اصلی
    foreach ($menu as $position => $menu_item) {
        if (isset($menu_item[2]) && $menu_item[2] === 'setia-content-generator') {
            $setia_menu_found = true;
            $setia_menu_data = $menu_item;
            break;
        }
    }
    
    // دریافت زیرمنوها
    if (isset($submenu['setia-content-generator'])) {
        $setia_submenus = $submenu['setia-content-generator'];
    }
    
    // تحلیل تکرارها
    $total_submenus = count($setia_submenus);
    $slugs = array_column($setia_submenus, 2);
    $unique_slugs = array_unique($slugs);
    $duplicate_count = $total_submenus - count($unique_slugs);
    
    // پیدا کردن منوهای تکراری
    $duplicates = array();
    $slug_counts = array_count_values($slugs);
    foreach ($slug_counts as $slug => $count) {
        if ($count > 1) {
            $duplicates[$slug] = $count;
        }
    }
    ?>
    
    <div class="wrap">
        <h1>🔍 بررسی منوهای پلاگین SETIA</h1>
        
        <div class="notice notice-info">
            <p><strong>📊 آمار کلی:</strong></p>
            <ul>
                <li>وضعیت منوی اصلی: <?php echo $setia_menu_found ? '✅ یافت شد' : '❌ یافت نشد'; ?></li>
                <li>تعداد کل زیرمنوها: <?php echo $total_submenus; ?></li>
                <li>منوهای یکتا: <?php echo count($unique_slugs); ?></li>
                <li>منوهای تکراری: <?php echo $duplicate_count; ?></li>
            </ul>
        </div>
        
        <?php if ($duplicate_count > 0): ?>
            <div class="notice notice-error">
                <h3>⚠️ منوهای تکراری شناسایی شد:</h3>
                <ul>
                    <?php foreach ($duplicates as $slug => $count): ?>
                        <li><strong><?php echo esc_html($slug); ?></strong> - تکرار <?php echo $count; ?> بار</li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php else: ?>
            <div class="notice notice-success">
                <p>✅ <strong>عالی!</strong> هیچ منوی تکراری یافت نشد.</p>
            </div>
        <?php endif; ?>
        
        <?php if ($setia_menu_found): ?>
            <h2>📋 اطلاعات منوی اصلی</h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>ویژگی</th>
                        <th>مقدار</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>عنوان</td>
                        <td><?php echo esc_html($setia_menu_data[0]); ?></td>
                    </tr>
                    <tr>
                        <td>Capability</td>
                        <td><?php echo esc_html($setia_menu_data[1]); ?></td>
                    </tr>
                    <tr>
                        <td>Slug</td>
                        <td><?php echo esc_html($setia_menu_data[2]); ?></td>
                    </tr>
                    <tr>
                        <td>آیکون</td>
                        <td><?php echo esc_html($setia_menu_data[6]); ?></td>
                    </tr>
                </tbody>
            </table>
        <?php endif; ?>
        
        <h2>📂 لیست زیرمنوها</h2>
        <?php if (!empty($setia_submenus)): ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>عنوان</th>
                        <th>Capability</th>
                        <th>Slug</th>
                        <th>تکرار</th>
                        <th>عملیات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($setia_submenus as $index => $submenu_item): ?>
                        <?php 
                        $slug = $submenu_item[2];
                        $is_duplicate = isset($duplicates[$slug]);
                        $row_class = $is_duplicate ? 'style="background-color: #ffebee;"' : '';
                        ?>
                        <tr <?php echo $row_class; ?>>
                            <td><?php echo $index + 1; ?></td>
                            <td><?php echo esc_html($submenu_item[0]); ?></td>
                            <td><?php echo esc_html($submenu_item[1]); ?></td>
                            <td><code><?php echo esc_html($slug); ?></code></td>
                            <td>
                                <?php if ($is_duplicate): ?>
                                    <span style="color: #d32f2f; font-weight: bold;">
                                        ❌ تکراری (<?php echo $duplicates[$slug]; ?>x)
                                    </span>
                                <?php else: ?>
                                    <span style="color: #388e3c;">✅ یکتا</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=' . $slug); ?>" 
                                   class="button button-small" target="_blank">
                                    مشاهده
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <div class="notice notice-warning">
                <p>⚠️ هیچ زیرمنویی برای پلاگین SETIA یافت نشد!</p>
            </div>
        <?php endif; ?>
        
        <h2>🔧 ابزارهای رفع مشکل</h2>
        <div class="card">
            <h3>عملیات پیشنهادی:</h3>
            <p>
                <a href="<?php echo plugin_dir_url(__FILE__) . 'reset-plugin-menus.php'; ?>" 
                   class="button button-primary" target="_blank">
                    🔄 بازنشانی منوهای پلاگین
                </a>
                
                <a href="<?php echo admin_url('plugins.php'); ?>" 
                   class="button">
                    📦 مدیریت پلاگین‌ها
                </a>
                
                <a href="javascript:location.reload();" 
                   class="button">
                    🔄 رفرش صفحه
                </a>
            </p>
        </div>
        
        <h2>📝 گزارش تشخیصی</h2>
        <div class="card">
            <h4>اطلاعات سیستم:</h4>
            <ul>
                <li><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></li>
                <li><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
                <li><strong>تاریخ بررسی:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                <li><strong>کاربر فعلی:</strong> <?php echo wp_get_current_user()->user_login; ?></li>
            </ul>
            
            <h4>وضعیت پلاگین SETIA:</h4>
            <?php
            $plugin_file = 'setia-content-generator/setia-content-generator.php';
            $is_active = is_plugin_active($plugin_file);
            $plugin_data = get_plugin_data(WP_PLUGIN_DIR . '/' . $plugin_file);
            ?>
            <ul>
                <li><strong>وضعیت:</strong> <?php echo $is_active ? '✅ فعال' : '❌ غیرفعال'; ?></li>
                <li><strong>نسخه:</strong> <?php echo isset($plugin_data['Version']) ? $plugin_data['Version'] : 'نامشخص'; ?></li>
                <li><strong>نام:</strong> <?php echo isset($plugin_data['Name']) ? $plugin_data['Name'] : 'نامشخص'; ?></li>
            </ul>
        </div>
        
        <?php if ($duplicate_count > 0): ?>
            <div class="notice notice-error">
                <h3>🚨 خلاصه مشکلات:</h3>
                <p>تعداد <?php echo $duplicate_count; ?> منوی تکراری شناسایی شد که باید رفع شوند:</p>
                <ol>
                    <li>از اسکریپت بازنشانی منوها استفاده کنید</li>
                    <li>پلاگین را غیرفعال و مجدداً فعال کنید</li>
                    <li>کش مرورگر و WordPress را پاک کنید</li>
                    <li>مجدداً این صفحه را بررسی کنید</li>
                </ol>
            </div>
        <?php else: ?>
            <div class="notice notice-success">
                <h3>✅ وضعیت عالی!</h3>
                <p>منوهای پلاگین SETIA به درستی تنظیم شده‌اند و هیچ تکراری وجود ندارد.</p>
            </div>
        <?php endif; ?>
    </div>
    
    <style>
        .card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .card h3, .card h4 {
            margin-top: 0;
        }
        code {
            background: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
    <?php
}

// اگر در حالت AJAX اجرا می‌شود
if (defined('DOING_AJAX') && DOING_AJAX) {
    setia_menu_checker_page();
    exit;
}

// اگر مستقیماً فراخوانی شده
if (!defined('WP_ADMIN')) {
    setia_menu_checker_page();
}
?>
