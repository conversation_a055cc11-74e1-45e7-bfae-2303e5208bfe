<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SETIA Debug Test</title>
    <link rel="stylesheet" href="assets/css/admin-settings.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
            max-width: 300px;
            max-height: 200px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div id="debug" class="debug-info">
        <div>Debug Console:</div>
        <div id="debug-log"></div>
    </div>

    <div class="setia-settings-wrapper">
        <!-- Simple Tab Test -->
        <div class="setia-form">
            <div class="setia-tabs">
                <div class="setia-tab-nav">
                    <button type="button" class="setia-tab-button active" data-tab="api">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        API
                    </button>
                    <button type="button" class="setia-tab-button" data-tab="content">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        محتوا
                    </button>
                </div>
            </div>

            <div class="setia-tab-content">
                <div class="setia-tab-pane active" id="tab-api">
                    <div class="setia-card">
                        <div class="setia-card-body">
                            <h3>API Tab Content</h3>
                            <input type="text" id="gemini_api_key" class="setia-input" placeholder="Test API Key">
                            <button type="button" id="test-apis" class="setia-button setia-button-primary">Test API</button>
                        </div>
                    </div>
                </div>

                <div class="setia-tab-pane" id="tab-content">
                    <div class="setia-card">
                        <div class="setia-card-body">
                            <h3>Content Tab</h3>
                            <p>This is the content tab.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="setia-footer">
                <button type="button" id="reset-settings" class="setia-button setia-button-danger">Reset</button>
            </div>
        </div>
    </div>

    <script>
        // Debug logging
        function debugLog(message) {
            const log = document.getElementById('debug-log');
            const time = new Date().toLocaleTimeString();
            log.innerHTML += `<div>[${time}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }

        // Override console.log for debugging
        const originalLog = console.log;
        console.log = function(...args) {
            debugLog(args.join(' '));
            originalLog.apply(console, args);
        };

        const originalError = console.error;
        console.error = function(...args) {
            debugLog('ERROR: ' + args.join(' '));
            originalError.apply(console, args);
        };

        debugLog('Starting debug test...');
        debugLog('jQuery version: ' + (window.jQuery ? $.fn.jquery : 'Not loaded'));
        
        $(document).ready(function() {
            debugLog('DOM ready');
            debugLog('Tab buttons found: ' + $('.setia-tab-button').length);
            debugLog('Tab panes found: ' + $('.setia-tab-pane').length);
            
            // Test tab clicking manually
            $('.setia-tab-button').on('click', function() {
                debugLog('Tab button clicked: ' + $(this).data('tab'));
            });
            
            $('#test-apis').on('click', function() {
                debugLog('Test API button clicked');
            });
            
            $('#reset-settings').on('click', function() {
                debugLog('Reset button clicked');
            });
        });
    </script>
    
    <script src="assets/js/settings-enhanced.js"></script>
    
    <script>
        $(document).ready(function() {
            debugLog('All scripts loaded');
            
            // Test if our functions are available
            if (typeof window.SETIA_Settings !== 'undefined') {
                debugLog('SETIA_Settings API available');
            } else {
                debugLog('SETIA_Settings API NOT available');
            }
        });
    </script>
</body>
</html>
