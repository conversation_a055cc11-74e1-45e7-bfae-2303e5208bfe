<?php
/**
 * بررسی دقیق وضعیت فعلی پلاگین SETIA
 */

// بارگذاری وردپرس
require_once('../../../wp-load.php');

// بررسی دسترسی
if (!current_user_can('manage_options')) {
    wp_die('شما اجازه دسترسی به این صفحه را ندارید.');
}

echo '<html dir="rtl"><head><meta charset="UTF-8">';
echo '<title>بررسی وضعیت پلاگین SETIA</title>';
echo '<style>
    body { font-family: Tahoma, Arial, sans-serif; direction: rtl; padding: 20px; background: #f1f1f1; }
    .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #28a745; }
    .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #dc3545; }
    .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #ffc107; }
    .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #17a2b8; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
    h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
    .test-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; }
    .test-link:hover { background: #005a87; color: white; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { padding: 10px; border: 1px solid #ddd; text-align: right; }
    th { background: #f8f9fa; font-weight: bold; }
</style></head><body>';

echo '<div class="container">';
echo '<h1>🔍 بررسی دقیق وضعیت فعلی پلاگین SETIA</h1>';

// 1. بررسی فعال بودن پلاگین
echo '<div class="section">';
echo '<h2>1️⃣ وضعیت فعال‌سازی پلاگین</h2>';

$plugin_file = 'setia-content-generator/setia-content-generator.php';
$is_active = is_plugin_active($plugin_file);

if ($is_active) {
    echo '<div class="success">✅ پلاگین SETIA فعال است</div>';
} else {
    echo '<div class="error">❌ پلاگین SETIA غیرفعال است</div>';
    echo '<div class="warning">⚠️ برای ادامه تست، ابتدا پلاگین را فعال کنید</div>';
    echo '<a href="/WP/wp-admin/plugins.php" class="test-link">رفتن به صفحه پلاگین‌ها</a>';
}

// بررسی وجود فایل اصلی پلاگین
$plugin_path = WP_PLUGIN_DIR . '/setia-content-generator/setia-content-generator.php';
if (file_exists($plugin_path)) {
    echo '<div class="success">✅ فایل اصلی پلاگین موجود است</div>';
} else {
    echo '<div class="error">❌ فایل اصلی پلاگین موجود نیست</div>';
}

echo '</div>';

// 2. بررسی بارگذاری کلاس‌ها
echo '<div class="section">';
echo '<h2>2️⃣ وضعیت بارگذاری کلاس‌ها</h2>';

$classes_status = [
    'SETIA_Content_Generator' => 'کلاس اصلی پلاگین',
    'SETIA_Scheduler' => 'کلاس زمانبندی',
    'SETIA_Ajax_Handlers' => 'کلاس مدیریت AJAX'
];

foreach ($classes_status as $class => $description) {
    if (class_exists($class)) {
        echo '<div class="success">✅ ' . $description . ' (' . $class . ') بارگذاری شده</div>';
    } else {
        echo '<div class="error">❌ ' . $description . ' (' . $class . ') بارگذاری نشده</div>';
    }
}

echo '</div>';

// 3. بررسی منوهای ادمین
echo '<div class="section">';
echo '<h2>3️⃣ وضعیت منوهای ادمین</h2>';

global $submenu;

if (isset($submenu['setia-content-generator'])) {
    echo '<div class="success">✅ منوی اصلی پلاگین موجود است</div>';
    
    echo '<table>';
    echo '<tr><th>نام منو</th><th>صفحه</th><th>دسترسی</th><th>وضعیت</th></tr>';
    
    $expected_menus = [
        'setia-scheduler' => 'زمانبندی محتوا',
        'setia-cron-settings' => 'کرون سرور',
        'setia-internal-cron' => 'کرون داخلی'
    ];
    
    foreach ($submenu['setia-content-generator'] as $menu_item) {
        $page_slug = $menu_item[2];
        $menu_title = $menu_item[0];
        $capability = $menu_item[1];
        
        if (isset($expected_menus[$page_slug])) {
            echo '<tr>';
            echo '<td>' . $menu_title . '</td>';
            echo '<td>' . $page_slug . '</td>';
            echo '<td>' . $capability . '</td>';
            echo '<td><span style="color: green;">✅ موجود</span></td>';
            echo '</tr>';
            unset($expected_menus[$page_slug]);
        }
    }
    
    // نمایش منوهای گم شده
    foreach ($expected_menus as $slug => $title) {
        echo '<tr>';
        echo '<td>' . $title . '</td>';
        echo '<td>' . $slug . '</td>';
        echo '<td>-</td>';
        echo '<td><span style="color: red;">❌ گم شده</span></td>';
        echo '</tr>';
    }
    
    echo '</table>';
    
} else {
    echo '<div class="error">❌ منوی اصلی پلاگین موجود نیست</div>';
}

echo '</div>';

// 4. بررسی دسترسی به صفحات
echo '<div class="section">';
echo '<h2>4️⃣ تست دسترسی به صفحات</h2>';

$pages_to_test = [
    'setia-content-generator' => 'صفحه اصلی',
    'setia-scheduler' => 'صفحه زمانبندی',
    'setia-settings' => 'صفحه تنظیمات',
    'setia-history' => 'صفحه تاریخچه'
];

foreach ($pages_to_test as $page => $title) {
    $url = admin_url('admin.php?page=' . $page);
    echo '<a href="' . $url . '" class="test-link" target="_blank">تست ' . $title . '</a>';
}

echo '</div>';

// 5. بررسی جداول دیتابیس
echo '<div class="section">';
echo '<h2>5️⃣ وضعیت جداول دیتابیس</h2>';

global $wpdb;

$tables_to_check = [
    $wpdb->prefix . 'setia_generated_content' => 'جدول تاریخچه محتوا',
    $wpdb->prefix . 'setia_content_schedules' => 'جدول زمانبندی‌ها',
    $wpdb->prefix . 'setia_scheduler_logs' => 'جدول لاگ زمانبندی'
];

echo '<table>';
echo '<tr><th>نام جدول</th><th>توضیحات</th><th>وضعیت</th><th>تعداد رکورد</th></tr>';

foreach ($tables_to_check as $table_name => $description) {
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    echo '<tr>';
    echo '<td>' . $table_name . '</td>';
    echo '<td>' . $description . '</td>';
    
    if ($table_exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        echo '<td><span style="color: green;">✅ موجود</span></td>';
        echo '<td>' . number_format($count) . '</td>';
    } else {
        echo '<td><span style="color: red;">❌ موجود نیست</span></td>';
        echo '<td>-</td>';
    }
    
    echo '</tr>';
}

echo '</table>';
echo '</div>';

// 6. خلاصه وضعیت
echo '<div class="section">';
echo '<h2>📋 خلاصه وضعیت</h2>';

$issues = [];
if (!$is_active) $issues[] = 'پلاگین غیرفعال است';
if (!class_exists('SETIA_Scheduler')) $issues[] = 'کلاس زمانبندی بارگذاری نشده';
if (!isset($submenu['setia-content-generator'])) $issues[] = 'منوی پلاگین موجود نیست';

if (empty($issues)) {
    echo '<div class="success">🎉 همه چیز آماده است! می‌توانید به مرحله بعد بروید.</div>';
} else {
    echo '<div class="error">⚠️ مشکلات شناسایی شده:</div>';
    echo '<ul>';
    foreach ($issues as $issue) {
        echo '<li>' . $issue . '</li>';
    }
    echo '</ul>';
}

echo '</div>';

echo '</div></body></html>';
?>
