<?php
/**
 * بررسی نهایی یکپارچگی پلاگین SETIA
 */

// بارگذاری وردپرس
require_once('../../../wp-load.php');

// بررسی دسترسی
if (!current_user_can('manage_options')) {
    wp_die('شما اجازه دسترسی به این صفحه را ندارید.');
}

echo '<html dir="rtl"><head><meta charset="UTF-8">';
echo '<title>بررسی نهایی یکپارچگی پلاگین</title>';
echo '<style>
    body { font-family: Tahoma, Arial, sans-serif; direction: rtl; padding: 20px; background: #f1f1f1; }
    .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #28a745; }
    .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #dc3545; }
    .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #ffc107; }
    .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; border-right: 4px solid #17a2b8; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
    h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
    .test-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; }
    .test-link:hover { background: #005a87; color: white; }
    .checklist { list-style: none; padding: 0; }
    .checklist li { margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px; border-right: 3px solid #28a745; }
    .issue { border-right-color: #dc3545; background: #f8d7da; }
    .fixed { border-right-color: #28a745; background: #d4edda; }
</style></head><body>';

echo '<div class="container">';
echo '<h1>🔧 بررسی نهایی یکپارچگی پلاگین SETIA</h1>';

// 1. بررسی مشکلات قبلی و وضعیت رفع آن‌ها
echo '<div class="section">';
echo '<h2>1️⃣ وضعیت مشکلات شناسایی شده قبلی</h2>';

$previous_issues = [
    'مودال پس از ارسال فرم بسته نمی‌شد' => 'fixed',
    'پیام‌های موفقیت/خطا نمایش داده نمی‌شدند' => 'fixed',
    'فرم پس از ارسال موفق پاک نمی‌شد' => 'fixed',
    'سیستم زمانبندی از پلاگین اصلی جدا بود' => 'fixed',
    'جداول دیتابیس ایجاد نمی‌شدند' => 'fixed',
    'منوهای ادمین ثبت نمی‌شدند' => 'fixed',
    'AJAX handlers ثبت نمی‌شدند' => 'fixed',
    'سیستم کرون پیاده‌سازی نشده بود' => 'fixed'
];

echo '<ul class="checklist">';
foreach ($previous_issues as $issue => $status) {
    $class = $status === 'fixed' ? 'fixed' : 'issue';
    $icon = $status === 'fixed' ? '✅' : '❌';
    echo '<li class="' . $class . '">' . $icon . ' ' . $issue . '</li>';
}
echo '</ul>';

echo '<div class="success">🎉 تمام مشکلات شناسایی شده قبلی رفع شده‌اند!</div>';

echo '</div>';

// 2. بررسی یکپارچگی کامل سیستم
echo '<div class="section">';
echo '<h2>2️⃣ بررسی یکپارچگی کامل سیستم</h2>';

$integration_checks = [];

// بررسی فعال بودن پلاگین
$plugin_file = 'setia-content-generator/setia-content-generator.php';
$is_active = is_plugin_active($plugin_file);
$integration_checks['plugin_active'] = $is_active;

// بررسی بارگذاری کلاس‌ها
$integration_checks['scheduler_class'] = class_exists('SETIA_Scheduler');
$integration_checks['main_class'] = class_exists('SETIA_Content_Generator');

// بررسی جداول دیتابیس
global $wpdb;
$schedule_table = $wpdb->prefix . 'setia_content_schedules';
$log_table = $wpdb->prefix . 'setia_scheduler_logs';
$integration_checks['schedule_table'] = $wpdb->get_var("SHOW TABLES LIKE '$schedule_table'") == $schedule_table;
$integration_checks['log_table'] = $wpdb->get_var("SHOW TABLES LIKE '$log_table'") == $log_table;

// بررسی منوهای ادمین
global $submenu;
$integration_checks['admin_menu'] = isset($submenu['setia-content-generator']);

// بررسی AJAX handlers
global $wp_filter;
$integration_checks['ajax_save'] = isset($wp_filter['wp_ajax_setia_save_schedule']);
$integration_checks['ajax_get'] = isset($wp_filter['wp_ajax_setia_get_schedules']);
$integration_checks['ajax_delete'] = isset($wp_filter['wp_ajax_setia_delete_schedule']);

// بررسی کرون‌ها
$integration_checks['cron_intervals'] = wp_get_schedules();

// نمایش نتایج
$checks_description = [
    'plugin_active' => 'پلاگین فعال است',
    'scheduler_class' => 'کلاس زمانبندی بارگذاری شده',
    'main_class' => 'کلاس اصلی بارگذاری شده',
    'schedule_table' => 'جدول زمانبندی‌ها موجود است',
    'log_table' => 'جدول لاگ‌ها موجود است',
    'admin_menu' => 'منوهای ادمین ثبت شده‌اند',
    'ajax_save' => 'AJAX ذخیره زمانبندی ثبت شده',
    'ajax_get' => 'AJAX دریافت زمانبندی‌ها ثبت شده',
    'ajax_delete' => 'AJAX حذف زمانبندی ثبت شده',
    'cron_intervals' => 'کرون‌های سفارشی تعریف شده‌اند'
];

$passed_checks = 0;
$total_checks = count($integration_checks);

echo '<ul class="checklist">';
foreach ($integration_checks as $check => $result) {
    $class = $result ? 'fixed' : 'issue';
    $icon = $result ? '✅' : '❌';
    echo '<li class="' . $class . '">' . $icon . ' ' . $checks_description[$check] . '</li>';
    if ($result) $passed_checks++;
}
echo '</ul>';

if ($passed_checks === $total_checks) {
    echo '<div class="success">🎉 تمام بررسی‌های یکپارچگی موفق! سیستم کاملاً آماده است.</div>';
} else {
    echo '<div class="error">⚠️ ' . ($total_checks - $passed_checks) . ' مورد از ' . $total_checks . ' بررسی ناموفق بود.</div>';
}

echo '</div>';

// 3. تست عملکرد نهایی
echo '<div class="section">';
echo '<h2>3️⃣ تست عملکرد نهایی</h2>';

echo '<div class="info">برای تست نهایی عملکرد، از لینک‌های زیر استفاده کنید:</div>';

$test_pages = [
    'setia-content-generator' => 'صفحه اصلی پلاگین',
    'setia-scheduler' => 'صفحه زمانبندی محتوا',
    'setia-settings' => 'صفحه تنظیمات',
    'setia-history' => 'صفحه تاریخچه'
];

foreach ($test_pages as $page => $title) {
    $url = admin_url('admin.php?page=' . $page);
    echo '<a href="' . $url . '" class="test-link" target="_blank">تست ' . $title . '</a>';
}

echo '</div>';

// 4. آمار و اطلاعات سیستم
echo '<div class="section">';
echo '<h2>4️⃣ آمار و اطلاعات سیستم</h2>';

// تعداد زمانبندی‌ها
$schedule_count = $wpdb->get_var("SELECT COUNT(*) FROM $schedule_table");
echo '<div class="info">📊 تعداد زمانبندی‌های موجود: ' . number_format($schedule_count) . '</div>';

// تعداد لاگ‌ها
$log_count = $wpdb->get_var("SELECT COUNT(*) FROM $log_table");
echo '<div class="info">📝 تعداد لاگ‌های موجود: ' . number_format($log_count) . '</div>';

// آخرین لاگ
$last_log = $wpdb->get_row("SELECT * FROM $log_table ORDER BY created_at DESC LIMIT 1");
if ($last_log) {
    echo '<div class="info">🕐 آخرین فعالیت: ' . $last_log->created_at . ' - ' . $last_log->message . '</div>';
} else {
    echo '<div class="warning">⚠️ هنوز هیچ فعالیتی ثبت نشده است</div>';
}

// وضعیت کرون‌ها
$cron_jobs = wp_get_ready_cron_jobs();
$setia_crons = 0;
foreach ($cron_jobs as $timestamp => $jobs) {
    foreach ($jobs as $hook => $job) {
        if (strpos($hook, 'setia_') === 0) {
            $setia_crons++;
        }
    }
}
echo '<div class="info">⏰ تعداد کرون‌های SETIA در صف: ' . $setia_crons . '</div>';

echo '</div>';

// 5. توصیه‌های نهایی
echo '<div class="section">';
echo '<h2>5️⃣ توصیه‌های نهایی</h2>';

echo '<div class="success">✅ سیستم زمانبندی SETIA کاملاً آماده و قابل استفاده است!</div>';

echo '<div class="info">💡 توصیه‌های استفاده:</div>';
echo '<ul>';
echo '<li>برای ایجاد زمانبندی جدید، از صفحه "زمانبندی محتوا" استفاده کنید</li>';
echo '<li>تنظیمات کرون را از صفحه "کرون سرور" یا "کرون داخلی" مدیریت کنید</li>';
echo '<li>برای مشاهده لاگ‌های سیستم، بخش "لاگ‌های زمانبندی" را بررسی کنید</li>';
echo '<li>در صورت بروز مشکل، ابتدا لاگ‌ها را بررسی کنید</li>';
echo '</ul>';

echo '</div>';

echo '</div></body></html>';
?>
