<?php
/**
 * صفحه زمانبندی تولید محتوا - نسخه بازنویسی شده
 * SETIA Content Generator Plugin
 * @version 2.0.0
 */

// جلوگیری از دسترسی مستقیم
if (!defined('ABSPATH')) {
    exit;
}

// دریافت آمار کلی
global $setia_scheduler;
$stats = $setia_scheduler ? $setia_scheduler->get_scheduler_stats() : array();
$system_status = $setia_scheduler ? $setia_scheduler->check_system_status() : array();
$frequencies = $setia_scheduler ? $setia_scheduler->get_supported_frequencies() : array();
?>

<div class="wrap setia-scheduler-wrap">
    <div class="setia-header">
        <h1 class="setia-title">
            <span class="setia-icon">⏰</span>
            زمانبندی تولید محتوا
            <span class="setia-version">v2.0</span>
        </h1>
        <p class="setia-subtitle">مدیریت هوشمند و خودکار تولید محتوا با قابلیت‌های پیشرفته</p>
    </div>

    <!-- نوار وضعیت سیستم -->
    <div class="setia-system-status <?php echo $system_status['overall'] ? 'status-ok' : 'status-error'; ?>">
        <div class="status-indicator">
            <span class="status-icon"><?php echo $system_status['overall'] ? '✅' : '❌'; ?></span>
            <span class="status-text">
                <?php echo $system_status['overall'] ? 'سیستم آماده است' : 'مشکل در سیستم'; ?>
            </span>
        </div>
        <?php if (!$system_status['overall']): ?>
        <div class="status-details">
            <?php if (!$system_status['cron_enabled']): ?>
                <span class="status-issue">⚠️ WordPress Cron غیرفعال است</span>
            <?php endif; ?>
            <?php if (!$system_status['tables_exist']): ?>
                <span class="status-issue">⚠️ جداول دیتابیس وجود ندارند</span>
            <?php endif; ?>
            <?php if (!$system_status['permissions']): ?>
                <span class="status-issue">⚠️ دسترسی کافی ندارید</span>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>

    <!-- آمار کلی -->
    <div class="setia-stats-grid">
        <div class="setia-stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
                <div class="stat-number"><?php echo intval($stats['total_schedules'] ?? 0); ?></div>
                <div class="stat-label">کل زمانبندی‌ها</div>
            </div>
        </div>

        <div class="setia-stat-card active">
            <div class="stat-icon">🟢</div>
            <div class="stat-content">
                <div class="stat-number"><?php echo intval($stats['active_schedules'] ?? 0); ?></div>
                <div class="stat-label">زمانبندی‌های فعال</div>
            </div>
        </div>

        <div class="setia-stat-card">
            <div class="stat-icon">📝</div>
            <div class="stat-content">
                <div class="stat-number"><?php echo intval($stats['total_generated'] ?? 0); ?></div>
                <div class="stat-label">کل محتوای تولید شده</div>
            </div>
        </div>

        <div class="setia-stat-card today">
            <div class="stat-icon">🌟</div>
            <div class="stat-content">
                <div class="stat-number"><?php echo intval($stats['today_generated'] ?? 0); ?></div>
                <div class="stat-label">تولید شده امروز</div>
            </div>
        </div>
    </div>

    <div class="setia-main-container">
        <!-- دکمه‌های اصلی -->
        <div class="setia-action-bar">
            <button type="button" class="setia-btn setia-btn-primary" id="add-schedule-btn">
                <span class="btn-icon">➕</span>
                افزودن زمانبندی جدید
            </button>

            <button type="button" class="setia-btn setia-btn-secondary" id="refresh-schedules-btn">
                <span class="btn-icon">🔄</span>
                بروزرسانی
            </button>

            <button type="button" class="setia-btn setia-btn-info" id="view-logs-btn">
                <span class="btn-icon">📋</span>
                مشاهده لاگ‌ها
            </button>

            <div class="action-info">
                <span class="info-item">
                    <strong>آخرین اجرا:</strong> <?php echo esc_html($stats['last_run'] ?? 'هرگز'); ?>
                </span>
                <span class="info-item">
                    <strong>اجرای بعدی:</strong> <?php echo esc_html($stats['next_run'] ?? 'نامشخص'); ?>
                </span>
            </div>

        <!-- جدول زمانبندی‌ها -->
        <div class="setia-card">
            <div class="setia-card-header">
                <h3>لیست زمانبندی‌ها</h3>
                <div class="card-actions">
                    <span class="schedules-count">تعداد: <span id="schedules-count">0</span></span>
                </div>
            </div>

            <div class="setia-card-body">
                <div class="setia-table-container">
                    <table class="setia-table" id="schedules-table">
                        <thead>
                            <tr>
                                <th>عنوان</th>
                                <th>موضوع</th>
                                <th>فرکانس</th>
                                <th>وضعیت</th>
                                <th>آخرین اجرا</th>
                                <th>اجرای بعدی</th>
                                <th>تولید شده</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody id="schedules-tbody">
                            <tr class="loading-row">
                                <td colspan="8" class="text-center">
                                    <div class="loading-spinner">
                                        <span class="spinner"></span>
                                        در حال بارگذاری...
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="empty-state" id="empty-state" style="display: none;">
                    <div class="empty-icon">📅</div>
                    <h3>هیچ زمانبندی‌ای وجود ندارد</h3>
                    <p>برای شروع، اولین زمانبندی خود را ایجاد کنید</p>
                    <button type="button" class="setia-btn setia-btn-primary" onclick="openScheduleModal()">
                        ایجاد زمانبندی جدید
                    </button>
                </div>
            </div>
    </div>
</div>

<!-- مودال افزودن/ویرایش زمانبندی -->
<div id="schedule-modal" class="setia-modal">
    <div class="setia-modal-content">
        <div class="setia-modal-header">
            <h3 id="modal-title">افزودن زمانبندی جدید</h3>
            <button type="button" class="setia-modal-close" onclick="closeScheduleModal()">
                <span class="close-icon">✕</span>
            </button>
        </div>

        <div class="setia-modal-body">
            <form id="schedule-form" onsubmit="saveSchedule(event)">
                <input type="hidden" id="schedule-id" value="">

                <div class="form-row">
                    <div class="form-group">
                        <label for="schedule-title">عنوان زمانبندی <span class="required">*</span></label>
                        <input type="text" id="schedule-title" class="form-control" placeholder="مثال: تولید مقاله روزانه" required>
                    </div>

                    <div class="form-group">
                        <label for="schedule-frequency">فرکانس <span class="required">*</span></label>
                        <select id="schedule-frequency" class="form-control" required>
                            <?php foreach ($frequencies as $key => $label): ?>
                                <option value="<?php echo esc_attr($key); ?>"><?php echo esc_html($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="schedule-topic">موضوع محتوا <span class="required">*</span></label>
                    <textarea id="schedule-topic" class="form-control" rows="3" placeholder="موضوع یا عنوان کلی محتوای مورد نظر را وارد کنید" required></textarea>
                </div>

                <div class="form-group">
                    <label for="schedule-keywords">کلمات کلیدی</label>
                    <input type="text" id="schedule-keywords" class="form-control" placeholder="کلمات کلیدی را با کاما جدا کنید">
                    <small class="form-help">کلمات کلیدی برای بهینه‌سازی SEO استفاده می‌شوند</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="schedule-category">دسته‌بندی</label>
                        <select id="schedule-category" class="form-control">
                            <option value="">انتخاب دسته‌بندی</option>
                            <?php
                            $categories = get_categories(array('hide_empty' => false));
                            foreach ($categories as $category):
                            ?>
                                <option value="<?php echo $category->term_id; ?>"><?php echo esc_html($category->name); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="schedule-daily-limit">حد روزانه</label>
                        <input type="number" id="schedule-daily-limit" class="form-control" value="1" min="1" max="10">
                        <small class="form-help">حداکثر تعداد محتوا در روز</small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="schedule-tone">لحن نوشتاری</label>
                        <select id="schedule-tone" class="form-control">
                            <option value="عادی">عادی</option>
                            <option value="رسمی">رسمی</option>
                            <option value="دوستانه">دوستانه</option>
                            <option value="تخصصی">تخصصی</option>
                            <option value="ساده">ساده</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="schedule-length">طول محتوا</label>
                        <select id="schedule-length" class="form-control">
                            <option value="کوتاه">کوتاه</option>
                            <option value="متوسط" selected>متوسط</option>
                            <option value="بلند">بلند</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="schedule-generate-image" checked>
                            <span class="checkmark"></span>
                            تولید تصویر شاخص
                        </label>
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="submit" class="setia-btn setia-btn-primary">
                        <span class="btn-icon">💾</span>
                        <span id="save-btn-text">ذخیره زمانبندی</span>
                    </button>
                    <button type="button" class="setia-btn setia-btn-secondary" onclick="closeScheduleModal()">
                        انصراف
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال مشاهده لاگ‌ها -->
<div id="logs-modal" class="setia-modal">
    <div class="setia-modal-content modal-large">
        <div class="setia-modal-header">
            <h3>لاگ‌های سیستم زمانبندی</h3>
            <button type="button" class="setia-modal-close" onclick="closeLogsModal()">
                <span class="close-icon">✕</span>
            </button>
        </div>

        <div class="setia-modal-body">
            <div class="logs-filters">
                <div class="filter-group">
                    <label for="log-schedule-filter">فیلتر بر اساس زمانبندی:</label>
                    <select id="log-schedule-filter" class="form-control" onchange="filterLogs()">
                        <option value="">همه زمانبندی‌ها</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="log-type-filter">نوع لاگ:</label>
                    <select id="log-type-filter" class="form-control" onchange="filterLogs()">
                        <option value="">همه انواع</option>
                        <option value="info">اطلاعات</option>
                        <option value="success">موفقیت</option>
                        <option value="warning">هشدار</option>
                        <option value="error">خطا</option>
                    </select>
                </div>

                <div class="filter-actions">
                    <button type="button" class="setia-btn setia-btn-secondary" onclick="refreshLogs()">
                        <span class="btn-icon">🔄</span>
                        بروزرسانی
                    </button>
                    <button type="button" class="setia-btn setia-btn-danger" onclick="clearLogs()">
                        <span class="btn-icon">🗑️</span>
                        پاک کردن لاگ‌ها
                    </button>
                </div>
            </div>

            <div class="logs-container">
                <div class="logs-table-container">
                    <table class="setia-table">
                        <thead>
                            <tr>
                                <th>زمان</th>
                                <th>زمانبندی</th>
                                <th>پیام</th>
                                <th>نوع</th>
                                <th>زمان اجرا</th>
                                <th>مصرف حافظه</th>
                            </tr>
                        </thead>
                        <tbody id="logs-tbody">
                            <tr class="loading-row">
                                <td colspan="6" class="text-center">
                                    <div class="loading-spinner">
                                        <span class="spinner"></span>
                                        در حال بارگذاری لاگ‌ها...
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="logs-pagination">
                    <button type="button" class="setia-btn setia-btn-secondary" id="load-more-logs" onclick="loadMoreLogs()">
                        بارگذاری بیشتر
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال تأیید حذف -->
<div id="confirm-modal" class="setia-modal">
    <div class="setia-modal-content modal-small">
        <div class="setia-modal-header">
            <h3 id="confirm-title">تأیید عملیات</h3>
        </div>

        <div class="setia-modal-body">
            <div class="confirm-content">
                <div class="confirm-icon">⚠️</div>
                <p id="confirm-message">آیا از انجام این عملیات اطمینان دارید؟</p>
            </div>

            <div class="modal-actions">
                <button type="button" class="setia-btn setia-btn-danger" id="confirm-yes">
                    بله، ادامه بده
                </button>
                <button type="button" class="setia-btn setia-btn-secondary" onclick="closeConfirmModal()">
                    انصراف
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* استایل‌های اصلی */
.setia-scheduler-wrap {
    direction: rtl;
    font-family: 'IRANSans', 'Vazir', Tahoma, sans-serif;
    background: #f8fafc;
    min-height: 100vh;
    padding: 20px 0;
}

.setia-header {
    text-align: center;
    margin-bottom: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 20px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.setia-title {
    font-size: 2.5rem;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.setia-icon {
    font-size: 3rem;
    animation: pulse 2s infinite;
}

.setia-version {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: normal;
}

.setia-subtitle {
    font-size: 1.1rem;
    margin: 10px 0 0 0;
    opacity: 0.9;
}

/* نوار وضعیت سیستم */
.setia-system-status {
    background: white;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-right: 5px solid;
}

.setia-system-status.status-ok {
    border-right-color: #10b981;
}

.setia-system-status.status-error {
    border-right-color: #ef4444;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.status-details {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.status-issue {
    background: #fef2f2;
    color: #dc2626;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.9rem;
}

/* آمار کلی */
.setia-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.setia-stat-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-top: 4px solid #e5e7eb;
}

.setia-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.setia-stat-card.active {
    border-top-color: #10b981;
}

.setia-stat-card.today {
    border-top-color: #f59e0b;
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1f2937;
    display: block;
    margin-bottom: 5px;
}

.stat-label {
    color: #6b7280;
    font-size: 0.9rem;
}

/* نوار عملیات */
.setia-action-bar {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.action-info {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    font-size: 0.9rem;
    color: #6b7280;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* دکمه‌ها */
.setia-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    white-space: nowrap;
}

.setia-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.setia-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.setia-btn-secondary {
    background: #f3f4f6;
    color: #374151;
}

.setia-btn-secondary:hover {
    background: #e5e7eb;
}

.setia-btn-info {
    background: #3b82f6;
    color: white;
}

.setia-btn-info:hover {
    background: #2563eb;
}

.setia-btn-danger {
    background: #ef4444;
    color: white;
}

.setia-btn-danger:hover {
    background: #dc2626;
}

.btn-icon {
    font-size: 1.1rem;
}

/* کارت‌ها */
.setia-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.setia-card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 20px 25px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.setia-card-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 1.3rem;
    font-weight: 600;
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.schedules-count {
    background: #667eea;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.setia-card-body {
    padding: 25px;
}

/* جداول */
.setia-table-container {
    overflow-x: auto;
    border-radius: 10px;
    border: 1px solid #e5e7eb;
}

.setia-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.setia-table th {
    background: #f9fafb;
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    white-space: nowrap;
}

.setia-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
}

.setia-table tbody tr:hover {
    background: #f9fafb;
}

.setia-table tbody tr:last-child td {
    border-bottom: none;
}

/* وضعیت‌ها */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.active {
    background: #d1fae5;
    color: #065f46;
}

.status-badge.inactive {
    background: #fee2e2;
    color: #991b1b;
}

.status-badge.running {
    background: #dbeafe;
    color: #1e40af;
}

/* دکمه‌های عملیات */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.action-btn.edit {
    background: #3b82f6;
    color: white;
}

.action-btn.edit:hover {
    background: #2563eb;
}

.action-btn.delete {
    background: #ef4444;
    color: white;
}

.action-btn.delete:hover {
    background: #dc2626;
}

.action-btn.toggle {
    background: #10b981;
    color: white;
}

.action-btn.toggle:hover {
    background: #059669;
}

.action-btn.run {
    background: #f59e0b;
    color: white;
}

.action-btn.run:hover {
    background: #d97706;
}

.action-btn.logs {
    background: #6b7280;
    color: white;
}

.action-btn.logs:hover {
    background: #4b5563;
}

/* حالت خالی */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 10px 0;
    color: #374151;
}

.empty-state p {
    margin: 0 0 30px 0;
    font-size: 1.1rem;
}

/* لودینگ */
.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 20px;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.text-center {
    text-align: center;
}

/* مودال‌ها */
.setia-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(8px);
}

.setia-modal.show {
    display: flex !important;
    opacity: 1;
}

.setia-modal-content {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.setia-modal.show .setia-modal-content {
    transform: scale(1);
}

.modal-large {
    max-width: 900px;
}

.modal-small {
    max-width: 400px;
}

.setia-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px 30px;
    border-radius: 20px 20px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.setia-modal-header h3 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.setia-modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.setia-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.close-icon {
    font-size: 1.2rem;
    font-weight: bold;
}

.setia-modal-body {
    padding: 30px;
}

/* فرم‌ها */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

.required {
    color: #ef4444;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control:invalid {
    border-color: #ef4444;
}

.form-help {
    display: block;
    margin-top: 5px;
    font-size: 0.8rem;
    color: #6b7280;
}

/* چک‌باکس‌ها */
.checkbox-group {
    margin: 20px 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: 500;
    color: #374151;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
}

/* عملیات مودال */
.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

body.modal-open {
    overflow: hidden;
}

/* فیلترها */
.logs-filters {
    background: #f9fafb;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 20px;
    align-items: end;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
}

.filter-actions {
    display: flex;
    gap: 10px;
}

.logs-table-container {
    max-height: 400px;
    overflow-y: auto;
    border-radius: 10px;
    border: 1px solid #e5e7eb;
}

.logs-pagination {
    text-align: center;
    margin-top: 20px;
}

/* تأیید */
.confirm-content {
    text-align: center;
    padding: 20px 0;
}

.confirm-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #f59e0b;
}

.confirm-content p {
    font-size: 1.1rem;
    color: #374151;
    margin: 0;
}

/* انیمیشن‌ها */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ریسپانسیو */
@media (max-width: 768px) {
    .setia-stats-grid {
        grid-template-columns: 1fr;
    }

    .setia-action-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .action-info {
        justify-content: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .logs-filters {
        grid-template-columns: 1fr;
    }

    .setia-table-container {
        font-size: 0.8rem;
    }

    .setia-table th,
    .setia-table td {
        padding: 10px 8px;
    }

    .action-buttons {
        flex-wrap: wrap;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // متغیرهای سراسری
    const ajaxUrl = '<?php echo admin_url('admin-ajax.php'); ?>';
    const nonce = '<?php echo wp_create_nonce('setia_scheduler_nonce'); ?>';
    let currentScheduleId = null;
    let logsPage = 1;
    let logsLoading = false;

    // تابع نمایش پیام
    function showMessage(message, type = 'success') {
        const messageClass = type === 'success' ? 'notice-success' : 'notice-error';
        const icon = type === 'success' ? '✅' : '❌';

        const messageHtml = `
            <div class="notice ${messageClass} is-dismissible" style="margin: 15px 0; animation: fadeInUp 0.5s ease;">
                <p style="display: flex; align-items: center; gap: 10px; margin: 0;">
                    <span style="font-size: 18px;">${icon}</span>
                    <span>${message}</span>
                </p>
            </div>
        `;

        $('.setia-scheduler-wrap').prepend(messageHtml);

        setTimeout(() => {
            $('.notice').fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
    }

    // تابع بارگذاری زمانبندی‌ها
    function loadSchedules() {
        $('#schedules-tbody').html(`
            <tr class="loading-row">
                <td colspan="8" class="text-center">
                    <div class="loading-spinner">
                        <span class="spinner"></span>
                        در حال بارگذاری...
                    </div>
                </td>
            </tr>
        `);

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_get_schedules',
                nonce: nonce
            },
            success: function(response) {
                if (response.success) {
                    displaySchedules(response.data);
                    updateSchedulesCount(response.data.length);
                } else {
                    showMessage('خطا در بارگذاری زمانبندی‌ها: ' + response.data, 'error');
                    showEmptyState();
                }
            },
            error: function() {
                showMessage('خطا در ارتباط با سرور', 'error');
                showEmptyState();
            }
        });
    }

    // تابع نمایش زمانبندی‌ها
    function displaySchedules(schedules) {
        if (schedules.length === 0) {
            showEmptyState();
            return;
        }

        $('#empty-state').hide();
        let html = '';

        schedules.forEach(schedule => {
            const statusClass = schedule.status == 1 ? 'active' : 'inactive';
            const statusText = schedule.status == 1 ? 'فعال' : 'غیرفعال';
            const statusIcon = schedule.status == 1 ? '🟢' : '🔴';

            html += `
                <tr data-schedule-id="${schedule.id}">
                    <td>
                        <strong>${schedule.title}</strong>
                        <br><small style="color: #6b7280;">#${schedule.id}</small>
                    </td>
                    <td>
                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                            ${schedule.topic}
                        </div>
                    </td>
                    <td>
                        <span class="frequency-badge">${getFrequencyLabel(schedule.frequency)}</span>
                    </td>
                    <td>
                        <span class="status-badge ${statusClass}">
                            ${statusIcon} ${statusText}
                        </span>
                    </td>
                    <td>
                        <small>${schedule.last_run || 'هرگز'}</small>
                    </td>
                    <td>
                        <small>${schedule.next_run || 'نامشخص'}</small>
                    </td>
                    <td>
                        <span class="generated-count">${schedule.generated_count || 0}</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn edit" onclick="editSchedule(${schedule.id})" title="ویرایش">
                                ✏️
                            </button>
                            <button class="action-btn toggle" onclick="toggleSchedule(${schedule.id})" title="تغییر وضعیت">
                                ${schedule.status == 1 ? '⏸️' : '▶️'}
                            </button>
                            <button class="action-btn run" onclick="runScheduleNow(${schedule.id})" title="اجرای فوری">
                                ⚡
                            </button>
                            <button class="action-btn logs" onclick="viewScheduleLogs(${schedule.id})" title="مشاهده لاگ‌ها">
                                📋
                            </button>
                            <button class="action-btn delete" onclick="deleteSchedule(${schedule.id})" title="حذف">
                                🗑️
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        $('#schedules-tbody').html(html);
    }

    // تابع نمایش حالت خالی
    function showEmptyState() {
        $('#schedules-tbody').empty();
        $('#empty-state').show();
    }

    // تابع بروزرسانی تعداد زمانبندی‌ها
    function updateSchedulesCount(count) {
        $('#schedules-count').text(count);
    }

    // تابع دریافت برچسب فرکانس
    function getFrequencyLabel(frequency) {
        const labels = {
            '15min': 'هر 15 دقیقه',
            '30min': 'هر 30 دقیقه',
            'hourly': 'هر ساعت',
            '2hours': 'هر 2 ساعت',
            '6hours': 'هر 6 ساعت',
            'daily': 'روزانه',
            'weekly': 'هفتگی'
        };
        return labels[frequency] || frequency;
    }

    // توابع مودال
    window.openScheduleModal = function(scheduleId = null) {
        currentScheduleId = scheduleId;

        if (scheduleId) {
            // حالت ویرایش
            $('#modal-title').text('ویرایش زمانبندی');
            $('#save-btn-text').text('بروزرسانی زمانبندی');
            loadScheduleData(scheduleId);
        } else {
            // حالت افزودن
            $('#modal-title').text('افزودن زمانبندی جدید');
            $('#save-btn-text').text('ذخیره زمانبندی');
            resetScheduleForm();
        }

        $('#schedule-modal').addClass('show');
        $('body').addClass('modal-open');
    };

    window.closeScheduleModal = function() {
        $('#schedule-modal').removeClass('show');
        $('body').removeClass('modal-open');
        currentScheduleId = null;
        resetScheduleForm();
    };

    window.openLogsModal = function() {
        $('#logs-modal').addClass('show');
        $('body').addClass('modal-open');
        loadLogs();
    };

    window.closeLogsModal = function() {
        $('#logs-modal').removeClass('show');
        $('body').removeClass('modal-open');
    };

    window.closeConfirmModal = function() {
        $('#confirm-modal').removeClass('show');
        $('body').removeClass('modal-open');
    };

    // تابع ریست فرم
    function resetScheduleForm() {
        $('#schedule-form')[0].reset();
        $('#schedule-id').val('');
        $('#schedule-daily-limit').val('1');
        $('#schedule-tone').val('عادی');
        $('#schedule-length').val('متوسط');
        $('#schedule-generate-image').prop('checked', true);
    }

    // تابع بارگذاری داده‌های زمانبندی برای ویرایش
    function loadScheduleData(scheduleId) {
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_get_schedule',
                schedule_id: scheduleId,
                nonce: nonce
            },
            success: function(response) {
                if (response.success) {
                    const schedule = response.data;
                    $('#schedule-id').val(schedule.id);
                    $('#schedule-title').val(schedule.title);
                    $('#schedule-topic').val(schedule.topic);
                    $('#schedule-keywords').val(schedule.keywords);
                    $('#schedule-frequency').val(schedule.frequency);
                    $('#schedule-category').val(schedule.category_id);
                    $('#schedule-daily-limit').val(schedule.daily_limit);
                    $('#schedule-tone').val(schedule.tone || 'عادی');
                    $('#schedule-length').val(schedule.length || 'متوسط');
                    $('#schedule-generate-image').prop('checked', schedule.generate_image == 1);
                } else {
                    showMessage('خطا در بارگذاری اطلاعات زمانبندی', 'error');
                }
            },
            error: function() {
                showMessage('خطا در ارتباط با سرور', 'error');
            }
        });
    }

    // تابع ذخیره زمانبندی
    window.saveSchedule = function(event) {
        event.preventDefault();

        const formData = {
            action: currentScheduleId ? 'setia_update_schedule' : 'setia_save_schedule',
            nonce: nonce,
            id: $('#schedule-id').val(),
            title: $('#schedule-title').val().trim(),
            topic: $('#schedule-topic').val().trim(),
            keywords: $('#schedule-keywords').val().trim(),
            frequency: $('#schedule-frequency').val(),
            category_id: $('#schedule-category').val(),
            daily_limit: $('#schedule-daily-limit').val(),
            tone: $('#schedule-tone').val(),
            length: $('#schedule-length').val(),
            generate_image: $('#schedule-generate-image').is(':checked') ? 1 : 0
        };

        // اعتبارسنجی
        if (!formData.title) {
            showMessage('لطفاً عنوان زمانبندی را وارد کنید', 'error');
            return;
        }

        if (!formData.topic) {
            showMessage('لطفاً موضوع محتوا را وارد کنید', 'error');
            return;
        }

        // نمایش لودینگ
        $('#save-btn-text').text('در حال ذخیره...');

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    closeScheduleModal();
                    loadSchedules();
                } else {
                    showMessage('خطا: ' + response.data, 'error');
                }
            },
            error: function() {
                showMessage('خطا در ارتباط با سرور', 'error');
            },
            complete: function() {
                $('#save-btn-text').text(currentScheduleId ? 'بروزرسانی زمانبندی' : 'ذخیره زمانبندی');
            }
        });
    };

    // تابع ویرایش زمانبندی
    window.editSchedule = function(scheduleId) {
        openScheduleModal(scheduleId);
    };

    // تابع حذف زمانبندی
    window.deleteSchedule = function(scheduleId) {
        $('#confirm-title').text('حذف زمانبندی');
        $('#confirm-message').text('آیا از حذف این زمانبندی اطمینان دارید؟ این عمل قابل بازگشت نیست.');

        $('#confirm-yes').off('click').on('click', function() {
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'setia_delete_schedule',
                    schedule_id: scheduleId,
                    nonce: nonce
                },
                success: function(response) {
                    if (response.success) {
                        showMessage('زمانبندی با موفقیت حذف شد', 'success');
                        loadSchedules();
                    } else {
                        showMessage('خطا در حذف زمانبندی: ' + response.data, 'error');
                    }
                },
                error: function() {
                    showMessage('خطا در ارتباط با سرور', 'error');
                }
            });
            closeConfirmModal();
        });

        $('#confirm-modal').addClass('show');
        $('body').addClass('modal-open');
    };

    // تابع تغییر وضعیت زمانبندی
    window.toggleSchedule = function(scheduleId) {
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_toggle_schedule',
                schedule_id: scheduleId,
                nonce: nonce
            },
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    loadSchedules();
                } else {
                    showMessage('خطا در تغییر وضعیت: ' + response.data, 'error');
                }
            },
            error: function() {
                showMessage('خطا در ارتباط با سرور', 'error');
            }
        });
    };

    // تابع اجرای فوری زمانبندی
    window.runScheduleNow = function(scheduleId) {
        $('#confirm-title').text('اجرای فوری زمانبندی');
        $('#confirm-message').text('آیا می‌خواهید این زمانبندی را الان اجرا کنید؟');

        $('#confirm-yes').off('click').on('click', function() {
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'setia_run_schedule_now',
                    schedule_id: scheduleId,
                    nonce: nonce
                },
                beforeSend: function() {
                    $('#confirm-yes').text('در حال اجرا...');
                },
                success: function(response) {
                    if (response.success) {
                        showMessage(response.data.message, 'success');
                        loadSchedules();
                    } else {
                        showMessage('خطا در اجرای زمانبندی: ' + response.data, 'error');
                    }
                },
                error: function() {
                    showMessage('خطا در ارتباط با سرور', 'error');
                },
                complete: function() {
                    $('#confirm-yes').text('بله، ادامه بده');
                }
            });
            closeConfirmModal();
        });

        $('#confirm-modal').addClass('show');
        $('body').addClass('modal-open');
    };

    // تابع مشاهده لاگ‌های زمانبندی
    window.viewScheduleLogs = function(scheduleId) {
        currentScheduleId = scheduleId;
        openLogsModal();
        loadLogs(scheduleId);
    };

    // تابع بارگذاری لاگ‌ها
    function loadLogs(scheduleId = null) {
        logsPage = 1;
        logsLoading = true;

        $('#logs-tbody').html(`
            <tr class="loading-row">
                <td colspan="6" class="text-center">
                    <div class="loading-spinner">
                        <span class="spinner"></span>
                        در حال بارگذاری لاگ‌ها...
                    </div>
                </td>
            </tr>
        `);

        const data = {
            action: 'setia_get_schedule_logs',
            nonce: nonce,
            page: logsPage,
            schedule_id: scheduleId || $('#log-schedule-filter').val(),
            type: $('#log-type-filter').val()
        };

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    displayLogs(response.data.logs);
                    updateLogFilters(response.data.schedules);
                } else {
                    $('#logs-tbody').html(`
                        <tr>
                            <td colspan="6" class="text-center">
                                خطا در بارگذاری لاگ‌ها: ${response.data}
                            </td>
                        </tr>
                    `);
                }
            },
            error: function() {
                $('#logs-tbody').html(`
                    <tr>
                        <td colspan="6" class="text-center">
                            خطا در ارتباط با سرور
                        </td>
                    </tr>
                `);
            },
            complete: function() {
                logsLoading = false;
            }
        });
    }

    // تابع نمایش لاگ‌ها
    function displayLogs(logs) {
        if (logs.length === 0) {
            $('#logs-tbody').html(`
                <tr>
                    <td colspan="6" class="text-center">
                        هیچ لاگی یافت نشد
                    </td>
                </tr>
            `);
            return;
        }

        let html = '';
        logs.forEach(log => {
            const typeClass = getLogTypeClass(log.type);
            const typeIcon = getLogTypeIcon(log.type);

            html += `
                <tr class="log-row ${typeClass}">
                    <td>
                        <small>${log.created_at}</small>
                    </td>
                    <td>
                        <strong>${log.schedule_title || 'نامشخص'}</strong>
                        <br><small>#${log.schedule_id}</small>
                    </td>
                    <td>
                        <div style="max-width: 300px; word-wrap: break-word;">
                            ${log.message}
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${typeClass}">
                            ${typeIcon} ${getLogTypeLabel(log.type)}
                        </span>
                    </td>
                    <td>
                        <small>${log.execution_time || '-'}</small>
                    </td>
                    <td>
                        <small>${log.memory_usage || '-'}</small>
                    </td>
                </tr>
            `;
        });

        $('#logs-tbody').html(html);
    }

    // توابع کمکی برای لاگ‌ها
    function getLogTypeClass(type) {
        const classes = {
            'info': 'info',
            'success': 'active',
            'warning': 'warning',
            'error': 'inactive'
        };
        return classes[type] || 'info';
    }

    function getLogTypeIcon(type) {
        const icons = {
            'info': 'ℹ️',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌'
        };
        return icons[type] || 'ℹ️';
    }

    function getLogTypeLabel(type) {
        const labels = {
            'info': 'اطلاعات',
            'success': 'موفقیت',
            'warning': 'هشدار',
            'error': 'خطا'
        };
        return labels[type] || type;
    }

    // تابع بروزرسانی فیلترهای لاگ
    function updateLogFilters(schedules) {
        let options = '<option value="">همه زمانبندی‌ها</option>';
        schedules.forEach(schedule => {
            options += `<option value="${schedule.id}">${schedule.title}</option>`;
        });
        $('#log-schedule-filter').html(options);
    }

    // توابع فیلتر و عملیات لاگ
    window.filterLogs = function() {
        loadLogs();
    };

    window.refreshLogs = function() {
        loadLogs();
    };

    window.clearLogs = function() {
        $('#confirm-title').text('پاک کردن لاگ‌ها');
        $('#confirm-message').text('آیا از پاک کردن تمام لاگ‌ها اطمینان دارید؟');

        $('#confirm-yes').off('click').on('click', function() {
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'setia_clear_schedule_logs',
                    nonce: nonce
                },
                success: function(response) {
                    if (response.success) {
                        showMessage('لاگ‌ها با موفقیت پاک شدند', 'success');
                        loadLogs();
                    } else {
                        showMessage('خطا در پاک کردن لاگ‌ها: ' + response.data, 'error');
                    }
                },
                error: function() {
                    showMessage('خطا در ارتباط با سرور', 'error');
                }
            });
            closeConfirmModal();
        });

        $('#confirm-modal').addClass('show');
        $('body').addClass('modal-open');
    };

    window.loadMoreLogs = function() {
        if (logsLoading) return;

        logsPage++;
        logsLoading = true;

        // TODO: پیاده‌سازی بارگذاری بیشتر
    };

    // رویدادهای صفحه
    $(document).ready(function() {
        // بارگذاری اولیه زمانبندی‌ها
        loadSchedules();

        // رویداد افزودن زمانبندی جدید
        $('#add-schedule-btn').on('click', function() {
            openScheduleModal();
        });

        // رویداد بروزرسانی
        $('#refresh-schedules-btn').on('click', function() {
            loadSchedules();
            showMessage('لیست زمانبندی‌ها بروزرسانی شد', 'success');
        });

        // رویداد مشاهده لاگ‌ها
        $('#view-logs-btn').on('click', function() {
            openLogsModal();
        });

        // بستن مودال با کلیک روی backdrop
        $('.setia-modal').on('click', function(e) {
            if (e.target === this) {
                if ($(this).attr('id') === 'schedule-modal') {
                    closeScheduleModal();
                } else if ($(this).attr('id') === 'logs-modal') {
                    closeLogsModal();
                } else if ($(this).attr('id') === 'confirm-modal') {
                    closeConfirmModal();
                }
            }
        });

        // بستن مودال با کلید Escape
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                if ($('#schedule-modal').hasClass('show')) {
                    closeScheduleModal();
                } else if ($('#logs-modal').hasClass('show')) {
                    closeLogsModal();
                } else if ($('#confirm-modal').hasClass('show')) {
                    closeConfirmModal();
                }
            }
        });

        // اعتبارسنجی فرم در زمان واقعی
        $('#schedule-title, #schedule-topic').on('input', function() {
            const $this = $(this);
            if ($this.val().trim() === '') {
                $this.addClass('error');
            } else {
                $this.removeClass('error');
            }
        });

        // بروزرسانی خودکار آمار هر 30 ثانیه
        setInterval(function() {
            // TODO: بروزرسانی آمار بدون بارگذاری مجدد کل صفحه
        }, 30000);

        // انیمیشن ورود عناصر
        $('.setia-stat-card').each(function(index) {
            $(this).css({
                'animation-delay': (index * 0.1) + 's',
                'animation': 'slideInRight 0.6s ease forwards'
            });
        });

        // افکت هاور برای دکمه‌های عملیات
        $(document).on('mouseenter', '.action-btn', function() {
            $(this).css('transform', 'scale(1.1)');
        }).on('mouseleave', '.action-btn', function() {
            $(this).css('transform', 'scale(1)');
        });

        // نمایش tooltip برای دکمه‌های عملیات
        $(document).on('mouseenter', '[title]', function() {
            const title = $(this).attr('title');
            if (title) {
                $(this).attr('data-original-title', title).removeAttr('title');
                // TODO: پیاده‌سازی tooltip سفارشی
            }
        });
    });

    // تابع بروزرسانی آمار
    function updateStats() {
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'setia_get_scheduler_stats',
                nonce: nonce
            },
            success: function(response) {
                if (response.success) {
                    // بروزرسانی آمار در صفحه
                    // TODO: پیاده‌سازی بروزرسانی آمار
                }
            }
        });
    }

    // تابع اضافه کردن انیمیشن به عناصر
    function animateElement(element, animation = 'fadeInUp') {
        $(element).css({
            'animation': animation + ' 0.5s ease forwards'
        });
    }

    // تابع نمایش نوتیفیکیشن پیشرفته
    function showAdvancedNotification(title, message, type = 'info', duration = 5000) {
        // TODO: پیاده‌سازی نوتیفیکیشن پیشرفته
    }

    // تابع اعتبارسنجی پیشرفته فرم
    function validateForm(formId) {
        let isValid = true;
        const $form = $(formId);

        $form.find('[required]').each(function() {
            const $field = $(this);
            if ($field.val().trim() === '') {
                $field.addClass('error');
                isValid = false;
            } else {
                $field.removeClass('error');
            }
        });

        return isValid;
    }

    // اضافه کردن CSS برای انیمیشن‌ها
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .error {
                border-color: #ef4444 !important;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
            }

            .loading-state {
                position: relative;
                overflow: hidden;
            }

            .loading-state::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
                animation: shimmer 1.5s infinite;
            }

            @keyframes shimmer {
                0% { left: -100%; }
                100% { left: 100%; }
            }
        `)
        .appendTo('head');

});
</script>
