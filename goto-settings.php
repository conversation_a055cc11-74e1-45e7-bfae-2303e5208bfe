<?php
/**
 * Direct redirect to SETIA settings with debug
 */

// WordPress environment
if (file_exists('../../../wp-config.php')) {
    require_once('../../../wp-config.php');
    require_once('../../../wp-load.php');
} else {
    die('WordPress not found');
}

if (!current_user_can('manage_options')) {
    die('Access denied');
}

// Force clear cache
wp_cache_flush();
update_option('setia_asset_version', time());

// Redirect to settings page with debug
$settings_url = admin_url('admin.php?page=setia-settings&setia_debug=1&nocache=' . time());

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Redirecting to SETIA Settings...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: #f1f1f1;
        }
        .redirect-box {
            background: white;
            border: 1px solid #ccd0d4;
            border-radius: 5px;
            padding: 30px;
            max-width: 500px;
            margin: 0 auto;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0073aa;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #0073aa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 3px;
        }
    </style>
    <meta http-equiv="refresh" content="3;url=<?php echo esc_url($settings_url); ?>">
</head>
<body>
    <div class="redirect-box">
        <h1>🚀 SETIA Settings</h1>
        <div class="spinner"></div>
        <p>در حال انتقال به صفحه تنظیمات SETIA...</p>
        
        <div class="info">
            <h3>📊 اطلاعات Debug</h3>
            <p><strong>زمان:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <p><strong>Asset Version:</strong> <?php echo get_option('setia_asset_version'); ?></p>
            <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
            <p><strong>URL هدف:</strong><br><small><?php echo esc_html($settings_url); ?></small></p>
        </div>
        
        <p><a href="<?php echo esc_url($settings_url); ?>" style="color: #0073aa; text-decoration: none; font-weight: bold;">🔗 کلیک کنید اگر انتقال خودکار کار نکرد</a></p>
        
        <hr style="margin: 30px 0;">
        
        <h3>🧪 لینک‌های تست اضافی</h3>
        <p><a href="debug-settings.php" target="_blank">🔍 گزارش Debug کامل</a></p>
        <p><a href="clear-cache.php" target="_blank">🗑️ پاک کردن کش</a></p>
        <p><a href="test-wordpress-settings.php" target="_blank">🧪 تست مستقیم</a></p>
    </div>

    <script>
        console.log('🚀 Redirecting to SETIA Settings...');
        console.log('Target URL:', '<?php echo esc_js($settings_url); ?>');
        
        // Countdown
        let countdown = 3;
        const countdownElement = document.createElement('p');
        countdownElement.style.fontSize = '18px';
        countdownElement.style.fontWeight = 'bold';
        countdownElement.style.color = '#0073aa';
        document.querySelector('.redirect-box').appendChild(countdownElement);
        
        const timer = setInterval(function() {
            countdownElement.textContent = `انتقال در ${countdown} ثانیه...`;
            countdown--;
            
            if (countdown < 0) {
                clearInterval(timer);
                countdownElement.textContent = 'در حال انتقال...';
                window.location.href = '<?php echo esc_js($settings_url); ?>';
            }
        }, 1000);
    </script>
</body>
</html>
