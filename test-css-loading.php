<?php
/*
 * تست بارگذاری CSS و تشخیص مشکلات
 */

// امنیت
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست بارگذاری CSS - SETIA</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f0f0f1;
            direction: rtl;
            text-align: right;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        
        .file-test {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        
        button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #005a87;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            direction: ltr;
            text-align: left;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 تست بارگذاری CSS - پلاگین SETIA</h1>
        
        <div class="test-section info">
            <h2>📋 اطلاعات محیط</h2>
            <p><strong>مسیر پلاگین:</strong> <?php echo plugin_dir_path(__FILE__); ?></p>
            <p><strong>URL پلاگین:</strong> <?php echo plugin_dir_url(__FILE__); ?></p>
            <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
            <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
        </div>

        <div class="test-section">
            <h2>📁 بررسی وجود فایل‌های CSS</h2>
            <?php
            $css_files = [
                'admin.css' => 'assets/css/admin.css',
                'admin-settings.css' => 'assets/css/admin-settings.css',
                'main-page-enhanced.css' => 'assets/css/main-page-enhanced.css',
                'history-advanced.css' => 'assets/css/history-advanced.css'
            ];
            
            foreach ($css_files as $name => $path) {
                $full_path = plugin_dir_path(__FILE__) . $path;
                $url = plugin_dir_url(__FILE__) . $path;
                $exists = file_exists($full_path);
                $readable = is_readable($full_path);
                $size = $exists ? filesize($full_path) : 0;
                
                echo '<div class="file-test ' . ($exists && $readable && $size > 0 ? 'success' : 'error') . '">';
                echo '<strong>' . $name . '</strong><br>';
                echo 'مسیر: ' . $path . '<br>';
                echo 'وجود دارد: ' . ($exists ? '✅ بله' : '❌ خیر') . '<br>';
                echo 'قابل خواندن: ' . ($readable ? '✅ بله' : '❌ خیر') . '<br>';
                echo 'حجم: ' . ($size > 0 ? number_format($size) . ' بایت' : '❌ خالی') . '<br>';
                echo 'URL: <a href="' . $url . '" target="_blank">' . $url . '</a>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h2>🔗 تست دسترسی مستقیم به فایل‌ها</h2>
            <p>برای تست دسترسی، روی لینک‌های زیر کلیک کنید:</p>
            <?php foreach ($css_files as $name => $path): ?>
                <div>
                    <a href="<?php echo plugin_dir_url(__FILE__) . $path; ?>" target="_blank" style="color: #0073aa;">
                        <?php echo $name; ?>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="test-section">
            <h2>⚙️ بررسی تنظیمات WordPress</h2>
            <?php
            $wp_debug = defined('WP_DEBUG') && WP_DEBUG;
            $wp_debug_log = defined('WP_DEBUG_LOG') && WP_DEBUG_LOG;
            $wp_debug_display = defined('WP_DEBUG_DISPLAY') && WP_DEBUG_DISPLAY;
            
            echo '<div class="file-test ' . ($wp_debug ? 'warning' : 'info') . '">';
            echo '<strong>WP_DEBUG:</strong> ' . ($wp_debug ? '✅ فعال' : '❌ غیرفعال') . '<br>';
            echo '<strong>WP_DEBUG_LOG:</strong> ' . ($wp_debug_log ? '✅ فعال' : '❌ غیرفعال') . '<br>';
            echo '<strong>WP_DEBUG_DISPLAY:</strong> ' . ($wp_debug_display ? '✅ فعال' : '❌ غیرفعال');
            echo '</div>';
            ?>
        </div>

        <div class="test-section">
            <h2>🧪 تست JavaScript برای بررسی بارگذاری CSS</h2>
            <button onclick="testCSSLoading()">تست بارگذاری CSS</button>
            <button onclick="checkWordPressHooks()">بررسی Hook های WordPress</button>
            <div id="js-test-results" style="margin-top: 15px;"></div>
        </div>

        <div class="test-section">
            <h2>🔧 راه‌حل‌های پیشنهادی</h2>
            <div class="code-block">
                <strong>1. پاکسازی کش:</strong><br>
                - کش مرورگر: Ctrl+Shift+R<br>
                - کش WordPress: استفاده از پلاگین کش<br>
                - کش CDN: پاکسازی از پنل CDN
            </div>
            
            <div class="code-block">
                <strong>2. بررسی مجوزهای فایل:</strong><br>
                chmod 644 assets/css/*.css<br>
                chmod 755 assets/css/
            </div>
            
            <div class="code-block">
                <strong>3. بررسی .htaccess:</strong><br>
                اطمینان حاصل کنید که فایل .htaccess مانع دسترسی به فایل‌های CSS نمی‌شود
            </div>
        </div>
    </div>

    <script>
        function testCSSLoading() {
            const results = document.getElementById('js-test-results');
            results.innerHTML = '<p>در حال تست...</p>';
            
            const cssFiles = [
                'assets/css/admin.css',
                'assets/css/admin-settings.css',
                'assets/css/main-page-enhanced.css',
                'assets/css/history-advanced.css'
            ];
            
            let testResults = '<h3>نتایج تست JavaScript:</h3>';
            let testPromises = [];
            
            cssFiles.forEach(file => {
                const promise = fetch(file)
                    .then(response => {
                        const status = response.ok ? '✅ موفق' : '❌ خطا';
                        const statusCode = response.status;
                        testResults += `<div class="${response.ok ? 'success' : 'error'}" style="margin: 5px 0; padding: 10px; border-radius: 4px;">
                            <strong>${file}:</strong> ${status} (کد: ${statusCode})
                        </div>`;
                    })
                    .catch(error => {
                        testResults += `<div class="error" style="margin: 5px 0; padding: 10px; border-radius: 4px;">
                            <strong>${file}:</strong> ❌ خطا - ${error.message}
                        </div>`;
                    });
                testPromises.push(promise);
            });
            
            Promise.all(testPromises).then(() => {
                results.innerHTML = testResults;
            });
        }
        
        function checkWordPressHooks() {
            const results = document.getElementById('js-test-results');
            results.innerHTML = '<h3>بررسی Hook های WordPress:</h3>';
            
            // بررسی وجود jQuery
            const jqueryExists = typeof jQuery !== 'undefined';
            results.innerHTML += `<div class="${jqueryExists ? 'success' : 'error'}" style="margin: 5px 0; padding: 10px; border-radius: 4px;">
                <strong>jQuery:</strong> ${jqueryExists ? '✅ بارگذاری شده' : '❌ بارگذاری نشده'}
            </div>`;
            
            // بررسی وجود wp object
            const wpExists = typeof wp !== 'undefined';
            results.innerHTML += `<div class="${wpExists ? 'success' : 'error'}" style="margin: 5px 0; padding: 10px; border-radius: 4px;">
                <strong>WordPress JS Object:</strong> ${wpExists ? '✅ موجود' : '❌ موجود نیست'}
            </div>`;
            
            // بررسی CSS links در head
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            const setiaCSS = Array.from(cssLinks).filter(link => link.href.includes('setia'));
            
            results.innerHTML += `<div class="info" style="margin: 5px 0; padding: 10px; border-radius: 4px;">
                <strong>تعداد کل CSS Links:</strong> ${cssLinks.length}<br>
                <strong>CSS های SETIA:</strong> ${setiaCSS.length}
            </div>`;
            
            if (setiaCSS.length > 0) {
                results.innerHTML += '<h4>CSS های SETIA یافت شده:</h4>';
                setiaCSS.forEach(link => {
                    results.innerHTML += `<div style="margin: 5px 0; padding: 5px; background: #f0f0f0; border-radius: 3px; font-family: monospace;">
                        ${link.href}
                    </div>`;
                });
            }
        }
    </script>
</body>
</html>
