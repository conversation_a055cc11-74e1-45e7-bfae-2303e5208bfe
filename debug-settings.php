<?php
/**
 * SETIA Settings Debug Tool
 * بررسی کامل وضعیت فایل‌ها و تنظیمات
 */

// WordPress environment
if (file_exists('../../../wp-config.php')) {
    require_once('../../../wp-config.php');
    require_once('../../../wp-load.php');
} else {
    die('WordPress not found');
}

if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo '<h1>🔍 SETIA Settings Debug Report</h1>';
echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.debug-section { background: #f9f9f9; border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
.info { color: blue; }
pre { background: #f0f0f0; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>';

// 1. بررسی فایل‌های اصلی
echo '<div class="debug-section">';
echo '<h2>📁 بررسی فایل‌های اصلی</h2>';

$files_to_check = [
    'templates/settings-page.php' => 'فایل PHP صفحه تنظیمات',
    'assets/css/admin-settings.css' => 'فایل CSS تنظیمات',
    'assets/js/settings-enhanced.js' => 'فایل JavaScript تنظیمات',
    'setia-content-generator.php' => 'فایل اصلی پلاگین'
];

foreach ($files_to_check as $file => $description) {
    $full_path = __DIR__ . '/' . $file;
    if (file_exists($full_path)) {
        $size = filesize($full_path);
        $modified = date('Y-m-d H:i:s', filemtime($full_path));
        echo "<p class='success'>✅ {$description}: موجود ({$size} bytes, آخرین تغییر: {$modified})</p>";
    } else {
        echo "<p class='error'>❌ {$description}: فایل موجود نیست در مسیر {$full_path}</p>";
    }
}
echo '</div>';

// 2. بررسی تنظیمات WordPress
echo '<div class="debug-section">';
echo '<h2>⚙️ بررسی تنظیمات WordPress</h2>';

$asset_version = get_option('setia_asset_version', 'not set');
echo "<p class='info'>📦 Asset Version: {$asset_version}</p>";

$plugin_url = plugin_dir_url(__FILE__);
echo "<p class='info'>🔗 Plugin URL: {$plugin_url}</p>";

$plugin_path = plugin_dir_path(__FILE__);
echo "<p class='info'>📂 Plugin Path: {$plugin_path}</p>";

echo '</div>';

// 3. بررسی محتوای فایل‌ها
echo '<div class="debug-section">';
echo '<h2>📄 بررسی محتوای فایل‌ها</h2>';

// بررسی فایل PHP
$php_file = __DIR__ . '/templates/settings-page.php';
if (file_exists($php_file)) {
    $php_content = file_get_contents($php_file);
    $has_tabs = strpos($php_content, 'setia-tab-button') !== false;
    $has_modern_design = strpos($php_content, 'setia-settings-wrapper') !== false;
    $has_windows11 = strpos($php_content, 'Windows 11') !== false;
    
    echo "<p class='".($has_tabs ? 'success' : 'error')."'>".($has_tabs ? '✅' : '❌')." فایل PHP شامل تب‌ها است</p>";
    echo "<p class='".($has_modern_design ? 'success' : 'error')."'>".($has_modern_design ? '✅' : '❌')." فایل PHP شامل طراحی مدرن است</p>";
}

// بررسی فایل CSS
$css_file = __DIR__ . '/assets/css/admin-settings.css';
if (file_exists($css_file)) {
    $css_content = file_get_contents($css_file);
    $has_variables = strpos($css_content, '--setia-primary') !== false;
    $has_tabs_style = strpos($css_content, '.setia-tab-button') !== false;
    $has_modern_style = strpos($css_content, 'Windows 11') !== false;
    
    echo "<p class='".($has_variables ? 'success' : 'error')."'>".($has_variables ? '✅' : '❌')." فایل CSS شامل متغیرهای طراحی است</p>";
    echo "<p class='".($has_tabs_style ? 'success' : 'error')."'>".($has_tabs_style ? '✅' : '❌')." فایل CSS شامل استایل تب‌ها است</p>";
    echo "<p class='".($has_modern_style ? 'success' : 'error')."'>".($has_modern_style ? '✅' : '❌')." فایل CSS شامل طراحی Windows 11 است</p>";
}

// بررسی فایل JS
$js_file = __DIR__ . '/assets/js/settings-enhanced.js';
if (file_exists($js_file)) {
    $js_content = file_get_contents($js_file);
    $has_tab_function = strpos($js_content, 'initializeTabNavigation') !== false;
    $has_enhanced = strpos($js_content, 'Enhanced Settings') !== false;
    $has_modern_js = strpos($js_content, 'v2.0') !== false;
    
    echo "<p class='".($has_tab_function ? 'success' : 'error')."'>".($has_tab_function ? '✅' : '❌')." فایل JS شامل تابع تب‌ها است</p>";
    echo "<p class='".($has_enhanced ? 'success' : 'error')."'>".($has_enhanced ? '✅' : '❌')." فایل JS نسخه بهبود یافته است</p>";
    echo "<p class='".($has_modern_js ? 'success' : 'error')."'>".($has_modern_js ? '✅' : '❌')." فایل JS نسخه 2.0 است</p>";
}

echo '</div>';

// 4. تست URL های فایل‌ها
echo '<div class="debug-section">';
echo '<h2>🌐 تست دسترسی به فایل‌ها</h2>';

$base_url = plugin_dir_url(__FILE__);
$test_files = [
    'assets/css/admin-settings.css',
    'assets/js/settings-enhanced.js'
];

foreach ($test_files as $file) {
    $url = $base_url . $file;
    echo "<p class='info'>🔗 <a href='{$url}' target='_blank'>{$file}</a></p>";
}

echo '</div>';

// 5. تولید لینک تست
echo '<div class="debug-section">';
echo '<h2>🧪 لینک‌های تست</h2>';

$settings_url = admin_url('admin.php?page=setia-settings');
$clear_cache_url = plugin_dir_url(__FILE__) . 'clear-cache.php';
$test_direct_url = plugin_dir_url(__FILE__) . 'test-direct.php';

echo "<p><a href='{$settings_url}' target='_blank'>🔧 صفحه تنظیمات SETIA</a></p>";
echo "<p><a href='{$clear_cache_url}' target='_blank'>🗑️ پاک کردن کش</a></p>";
echo "<p><a href='{$test_direct_url}' target='_blank'>🧪 تست مستقیم</a></p>";

echo '</div>';

echo '<p><strong>گزارش تولید شده در: ' . date('Y-m-d H:i:s') . '</strong></p>';
?>
