<?php
/**
 * تست ساده برای بررسی مشکل پلاگین
 */

echo "<h2>تست ساده پلاگین SETIA</h2>";

// تست 1: بررسی وجود فایل
if (file_exists('setia-content-generator.php')) {
    echo "<p style='color: green;'>✅ فایل اصلی موجود است</p>";
} else {
    echo "<p style='color: red;'>❌ فایل اصلی موجود نیست</p>";
    exit;
}

// تست 2: بررسی syntax
echo "<h3>تست Syntax</h3>";
$content = file_get_contents('setia-content-generator.php');

// بررسی آکولادها
$open = substr_count($content, '{');
$close = substr_count($content, '}');

if ($open === $close) {
    echo "<p style='color: green;'>✅ آکولادها متعادل هستند ($open باز، $close بسته)</p>";
} else {
    echo "<p style='color: red;'>❌ آکولادها نامتعادل هستند ($open باز، $close بسته)</p>";
}

// تست 3: بررسی کلاس
if (strpos($content, 'class SETIA_Content_Generator') !== false) {
    echo "<p style='color: green;'>✅ کلاس اصلی تعریف شده</p>";
} else {
    echo "<p style='color: red;'>❌ کلاس اصلی تعریف نشده</p>";
}

// تست 4: بررسی پایان کلاس
if (strpos($content, '} // پایان کلاس') !== false) {
    echo "<p style='color: green;'>✅ پایان کلاس موجود است</p>";
} else {
    echo "<p style='color: red;'>❌ پایان کلاس موجود نیست</p>";
}

// تست 5: بررسی راه‌اندازی
if (strpos($content, '$setia_content_generator = new SETIA_Content_Generator()') !== false) {
    echo "<p style='color: green;'>✅ راه‌اندازی افزونه موجود است</p>";
} else {
    echo "<p style='color: red;'>❌ راه‌اندازی افزونه موجود نیست</p>";
}

// تست 6: تلاش برای include
echo "<h3>تست Include</h3>";
try {
    ob_start();
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    include_once 'setia-content-generator.php';
    
    $errors = ob_get_clean();
    
    if (empty($errors)) {
        echo "<p style='color: green;'>✅ فایل بدون خطا include شد</p>";
        
        if (class_exists('SETIA_Content_Generator')) {
            echo "<p style='color: green;'>✅ کلاس با موفقیت بارگذاری شد</p>";
        } else {
            echo "<p style='color: red;'>❌ کلاس بارگذاری نشد</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ خطا در include:</p>";
        echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
        echo htmlspecialchars($errors);
        echo "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (ParseError $e) {
    echo "<p style='color: red;'>❌ Parse Error: " . htmlspecialchars($e->getMessage()) . " در خط " . $e->getLine() . "</p>";
} catch (Error $e) {
    echo "<p style='color: red;'>❌ Fatal Error: " . htmlspecialchars($e->getMessage()) . " در خط " . $e->getLine() . "</p>";
}

echo "<hr>";
echo "<p><strong>نتیجه:</strong> اگر همه موارد سبز هستند، پلاگین باید کار کند.</p>";
?>
