<!DOCTYPE html>
<html dir="rtl" lang="fa">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست AJAX زمانبندی</title>
    <style>
        body {
            font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f1f1f1;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        #results {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 تست AJAX زمانبندی SETIA</h1>
        
        <div class="test-section">
            <h3>تست دریافت زمانبندی‌ها</h3>
            <button onclick="testGetSchedules()">دریافت لیست زمانبندی‌ها</button>
        </div>
        
        <div class="test-section">
            <h3>تست ذخیره زمانبندی جدید</h3>
            <button onclick="testSaveSchedule()">ایجاد زمانبندی تست</button>
        </div>
        
        <div class="test-section">
            <h3>تست دریافت لاگ‌ها</h3>
            <button onclick="testGetLogs()">دریافت لاگ‌های زمانبندی</button>
        </div>
        
        <div id="results">
            <p class="info">نتایج تست‌ها اینجا نمایش داده می‌شوند...</p>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        const ajaxUrl = '/WP/wp-admin/admin-ajax.php';
        
        // دریافت nonce از WordPress
        function getNonce() {
            return new Promise((resolve, reject) => {
                $.get('/WP/wp-admin/admin.php?page=setia-scheduler', function(data) {
                    const match = data.match(/var nonce = '([^']+)'/);
                    if (match) {
                        resolve(match[1]);
                    } else {
                        reject('Nonce not found');
                    }
                }).fail(reject);
            });
        }
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const p = document.createElement('p');
            p.className = type;
            p.innerHTML = new Date().toLocaleTimeString('fa-IR') + ' - ' + message;
            results.appendChild(p);
            results.scrollTop = results.scrollHeight;
        }
        
        async function testGetSchedules() {
            addResult('🔄 در حال تست دریافت زمانبندی‌ها...', 'info');
            
            try {
                const nonce = await getNonce();
                addResult('✅ Nonce دریافت شد: ' + nonce.substring(0, 10) + '...', 'success');
                
                $.ajax({
                    url: ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'setia_get_schedules',
                        nonce: nonce
                    },
                    success: function(response) {
                        addResult('✅ پاسخ دریافت شد: ' + JSON.stringify(response), 'success');
                        
                        if (response.success) {
                            addResult('✅ تست موفق: ' + (response.data.schedules ? response.data.schedules.length + ' زمانبندی یافت شد' : 'هیچ زمانبندی یافت نشد'), 'success');
                        } else {
                            addResult('❌ خطا در پاسخ: ' + (response.data ? response.data.message : 'خطای نامشخص'), 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        addResult('❌ خطا در AJAX: ' + error + ' (Status: ' + status + ')', 'error');
                        addResult('📋 Response Text: ' + xhr.responseText.substring(0, 200) + '...', 'info');
                    }
                });
                
            } catch (error) {
                addResult('❌ خطا در دریافت nonce: ' + error, 'error');
            }
        }
        
        async function testSaveSchedule() {
            addResult('🔄 در حال تست ذخیره زمانبندی...', 'info');
            
            try {
                const nonce = await getNonce();
                
                $.ajax({
                    url: ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'setia_save_schedule',
                        nonce: nonce,
                        title: 'تست زمانبندی',
                        topic: 'موضوع تست',
                        keywords: 'کلمه کلیدی تست',
                        frequency: 'daily',
                        status: 'active',
                        schedule_id: 0
                    },
                    success: function(response) {
                        addResult('✅ پاسخ دریافت شد: ' + JSON.stringify(response), 'success');
                        
                        if (response.success) {
                            addResult('✅ تست موفق: زمانبندی با موفقیت ذخیره شد', 'success');
                        } else {
                            addResult('❌ خطا در ذخیره: ' + (response.data ? response.data.message : 'خطای نامشخص'), 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        addResult('❌ خطا در AJAX: ' + error + ' (Status: ' + status + ')', 'error');
                        addResult('📋 Response Text: ' + xhr.responseText.substring(0, 200) + '...', 'info');
                    }
                });
                
            } catch (error) {
                addResult('❌ خطا در دریافت nonce: ' + error, 'error');
            }
        }
        
        async function testGetLogs() {
            addResult('🔄 در حال تست دریافت لاگ‌ها...', 'info');
            
            try {
                const nonce = await getNonce();
                
                $.ajax({
                    url: ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'setia_get_scheduler_logs',
                        nonce: nonce
                    },
                    success: function(response) {
                        addResult('✅ پاسخ دریافت شد: ' + JSON.stringify(response), 'success');
                        
                        if (response.success) {
                            addResult('✅ تست موفق: ' + (response.data.logs ? response.data.logs.length + ' لاگ یافت شد' : 'هیچ لاگی یافت نشد'), 'success');
                        } else {
                            addResult('❌ خطا در دریافت لاگ: ' + (response.data ? response.data.message : 'خطای نامشخص'), 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        addResult('❌ خطا در AJAX: ' + error + ' (Status: ' + status + ')', 'error');
                        addResult('📋 Response Text: ' + xhr.responseText.substring(0, 200) + '...', 'info');
                    }
                });
                
            } catch (error) {
                addResult('❌ خطا در دریافت nonce: ' + error, 'error');
            }
        }
        
        // تست اولیه
        $(document).ready(function() {
            addResult('🚀 سیستم تست آماده است', 'success');
        });
    </script>
</body>
</html>
