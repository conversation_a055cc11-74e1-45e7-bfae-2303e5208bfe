<?php
/**
 * تست CSS در محیط WordPress
 * این فایل برای تست بارگذاری CSS در محیط واقعی WordPress استفاده می‌شود
 */

// امنیت WordPress
if (!defined('ABSPATH')) {
    // تلاش برای بارگذاری WordPress
    $wp_config_paths = [
        '../../../wp-config.php',
        '../../../../wp-config.php',
        '../wp-config.php'
    ];
    
    foreach ($wp_config_paths as $path) {
        if (file_exists($path)) {
            require_once($path);
            break;
        }
    }
    
    if (!defined('ABSPATH')) {
        die('WordPress not found. Please run this file from WordPress admin.');
    }
}

// بررسی دسترسی ادمین
if (!current_user_can('manage_options')) {
    wp_die('شما دسترسی لازم برای مشاهده این صفحه را ندارید.');
}

// تنظیم header
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست CSS در WordPress - SETIA</title>
    
    <?php
    // بارگذاری CSS های WordPress
    wp_head();
    
    // بارگذاری مستقیم CSS های SETIA
    $plugin_url = plugin_dir_url(__FILE__);
    $css_version = time();
    ?>
    
    <link rel="stylesheet" href="<?php echo $plugin_url; ?>assets/css/admin.css?v=<?php echo $css_version; ?>">
    <link rel="stylesheet" href="<?php echo $plugin_url; ?>assets/css/admin-settings.css?v=<?php echo $css_version; ?>">
    <link rel="stylesheet" href="<?php echo $plugin_url; ?>assets/css/test-simple.css?v=<?php echo $css_version; ?>">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f1;
            direction: rtl;
            text-align: right;
        }
        
        .wp-test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #0073aa;
            padding-bottom: 10px;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            direction: ltr;
            text-align: left;
            overflow-x: auto;
        }
        
        button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        button:hover {
            background: #005a87;
        }
        
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="wp-admin">
    <div class="wp-test-container">
        <h1>🔧 تست CSS در محیط WordPress - پلاگین SETIA</h1>
        
        <div class="test-section">
            <h2>📊 اطلاعات محیط WordPress</h2>
            <div class="info-box">
                <strong>نسخه WordPress:</strong> <?php echo get_bloginfo('version'); ?><br>
                <strong>نسخه PHP:</strong> <?php echo PHP_VERSION; ?><br>
                <strong>پوسته فعال:</strong> <?php echo wp_get_theme()->get('Name'); ?><br>
                <strong>URL سایت:</strong> <?php echo home_url(); ?><br>
                <strong>URL ادمین:</strong> <?php echo admin_url(); ?><br>
                <strong>مسیر پلاگین:</strong> <?php echo plugin_dir_path(__FILE__); ?><br>
                <strong>URL پلاگین:</strong> <?php echo plugin_dir_url(__FILE__); ?>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📁 بررسی فایل‌های CSS</h2>
            <?php
            $css_files = [
                'admin.css' => 'assets/css/admin.css',
                'admin-settings.css' => 'assets/css/admin-settings.css',
                'main-page-enhanced.css' => 'assets/css/main-page-enhanced.css',
                'history-advanced.css' => 'assets/css/history-advanced.css',
                'test-simple.css' => 'assets/css/test-simple.css'
            ];
            
            foreach ($css_files as $name => $path) {
                $full_path = plugin_dir_path(__FILE__) . $path;
                $url = plugin_dir_url(__FILE__) . $path;
                $exists = file_exists($full_path);
                $readable = is_readable($full_path);
                $size = $exists ? filesize($full_path) : 0;
                $writable = is_writable($full_path);
                
                $box_class = ($exists && $readable && $size > 0) ? 'success-box' : 'error-box';
                
                echo '<div class="' . $box_class . '">';
                echo '<strong>' . $name . '</strong><br>';
                echo 'مسیر: ' . $path . '<br>';
                echo 'وجود دارد: ' . ($exists ? '✅ بله' : '❌ خیر') . '<br>';
                echo 'قابل خواندن: ' . ($readable ? '✅ بله' : '❌ خیر') . '<br>';
                echo 'قابل نوشتن: ' . ($writable ? '✅ بله' : '❌ خیر') . '<br>';
                echo 'حجم: ' . ($size > 0 ? number_format($size) . ' بایت' : '❌ خالی') . '<br>';
                echo 'URL: <a href="' . $url . '" target="_blank">' . $url . '</a>';
                echo '</div>';
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>🔗 تست WordPress Hooks</h2>
            <?php
            // بررسی وضعیت پلاگین
            $plugin_file = 'setia-content-generator/setia-content-generator.php';
            $is_plugin_active = is_plugin_active($plugin_file);
            
            echo '<div class="' . ($is_plugin_active ? 'success-box' : 'error-box') . '">';
            echo '<strong>وضعیت پلاگین SETIA:</strong> ' . ($is_plugin_active ? '✅ فعال' : '❌ غیرفعال');
            echo '</div>';
            
            // بررسی hooks
            global $wp_filter;
            $admin_enqueue_hooks = isset($wp_filter['admin_enqueue_scripts']) ? count($wp_filter['admin_enqueue_scripts']) : 0;
            
            echo '<div class="info-box">';
            echo '<strong>تعداد admin_enqueue_scripts hooks:</strong> ' . $admin_enqueue_hooks . '<br>';
            
            if (isset($wp_filter['admin_enqueue_scripts'])) {
                echo '<strong>Hooks موجود:</strong><br>';
                foreach ($wp_filter['admin_enqueue_scripts'] as $priority => $hooks) {
                    foreach ($hooks as $hook_name => $hook_data) {
                        if (strpos($hook_name, 'setia') !== false || strpos($hook_name, 'SETIA') !== false) {
                            echo '- ' . $hook_name . ' (اولویت: ' . $priority . ')<br>';
                        }
                    }
                }
            }
            echo '</div>';
            ?>
        </div>
        
        <div class="test-section">
            <h2>🧪 تست‌های JavaScript</h2>
            <button onclick="testCSSInWordPress()">تست CSS در WordPress</button>
            <button onclick="testWordPressEnqueue()">تست WordPress Enqueue</button>
            <button onclick="simulateAdminPage()">شبیه‌سازی صفحه ادمین</button>
            
            <div class="results" id="wpTestResults"></div>
        </div>
        
        <div class="test-section setia-test-container">
            <h2 class="setia-test-title">🎨 تست استایل‌های SETIA</h2>
            <div class="setia-test-content">
                این بخش برای تست استایل‌های پلاگین SETIA در محیط WordPress طراحی شده است.
                اگر این متن با استایل مناسب نمایش داده می‌شود، یعنی CSS به درستی بارگذاری شده است.
            </div>
            
            <div class="setia-gradient-bg" style="margin-top: 15px; padding: 15px; border-radius: 8px;">
                تست Gradient Background در WordPress
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 ابزارهای تشخیص و رفع مشکل</h2>
            <div class="code-block">
                <strong>دستورات مفید برای تشخیص مشکل:</strong><br><br>
                
                1. بررسی لاگ خطاهای WordPress:<br>
                tail -f <?php echo WP_CONTENT_DIR; ?>/debug.log<br><br>
                
                2. بررسی مجوزهای فایل:<br>
                ls -la <?php echo plugin_dir_path(__FILE__); ?>assets/css/<br><br>
                
                3. تست دسترسی مستقیم:<br>
                curl -I "<?php echo plugin_dir_url(__FILE__); ?>assets/css/admin.css"<br><br>
                
                4. پاکسازی کش WordPress:<br>
                - حذف فایل‌های کش از wp-content/cache/<br>
                - غیرفعال کردن موقت پلاگین‌های کش<br>
                - پاکسازی کش مرورگر
            </div>
        </div>
    </div>

    <script>
        function testCSSInWordPress() {
            const results = document.getElementById('wpTestResults');
            results.innerHTML = 'تست CSS در محیط WordPress...\n\n';
            
            // بررسی jQuery WordPress
            const jqueryWP = typeof jQuery !== 'undefined';
            results.innerHTML += `jQuery WordPress: ${jqueryWP ? '✅ بارگذاری شده' : '❌ بارگذاری نشده'}\n`;
            
            // بررسی wp object
            const wpObject = typeof wp !== 'undefined';
            results.innerHTML += `WordPress JS Object: ${wpObject ? '✅ موجود' : '❌ موجود نیست'}\n`;
            
            // بررسی admin bar
            const adminBar = document.getElementById('wpadminbar');
            results.innerHTML += `WordPress Admin Bar: ${adminBar ? '✅ موجود' : '❌ موجود نیست'}\n`;
            
            // بررسی CSS links
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            const setiaCSS = Array.from(cssLinks).filter(link => 
                link.href.includes('setia') || 
                link.href.includes('admin.css') || 
                link.href.includes('admin-settings.css')
            );
            
            results.innerHTML += `\nتعداد کل CSS Links: ${cssLinks.length}\n`;
            results.innerHTML += `CSS های SETIA: ${setiaCSS.length}\n\n`;
            
            if (setiaCSS.length > 0) {
                results.innerHTML += 'CSS های SETIA یافت شده:\n';
                setiaCSS.forEach(link => {
                    results.innerHTML += `- ${link.href}\n`;
                });
            }
        }
        
        function testWordPressEnqueue() {
            const results = document.getElementById('wpTestResults');
            results.innerHTML = 'تست WordPress Enqueue System...\n\n';
            
            // تست دسترسی به فایل‌ها
            const testFiles = [
                '<?php echo plugin_dir_url(__FILE__); ?>assets/css/admin.css',
                '<?php echo plugin_dir_url(__FILE__); ?>assets/css/admin-settings.css',
                '<?php echo plugin_dir_url(__FILE__); ?>assets/css/test-simple.css'
            ];
            
            testFiles.forEach(async (file, index) => {
                try {
                    const response = await fetch(file);
                    const status = response.ok ? '✅ موفق' : '❌ خطا';
                    results.innerHTML += `${index + 1}. ${file.split('/').pop()}: ${status} (${response.status})\n`;
                } catch (error) {
                    results.innerHTML += `${index + 1}. ${file.split('/').pop()}: ❌ خطای شبکه\n`;
                }
            });
        }
        
        function simulateAdminPage() {
            const results = document.getElementById('wpTestResults');
            results.innerHTML = 'شبیه‌سازی صفحه ادمین WordPress...\n\n';
            
            // اضافه کردن کلاس‌های ادمین
            document.body.classList.add('wp-admin', 'admin-color-fresh');
            
            // تست computed styles
            const testElement = document.querySelector('.setia-test-container');
            if (testElement) {
                const styles = window.getComputedStyle(testElement);
                results.innerHTML += `Background: ${styles.backgroundColor}\n`;
                results.innerHTML += `Border: ${styles.border}\n`;
                results.innerHTML += `Direction: ${styles.direction}\n`;
                results.innerHTML += `Font Family: ${styles.fontFamily}\n`;
            }
            
            results.innerHTML += '\n✅ کلاس‌های ادمین اضافه شدند\n';
        }
        
        // تست خودکار هنگام بارگذاری
        window.addEventListener('load', function() {
            console.log('🚀 صفحه تست WordPress بارگذاری شد');
            setTimeout(testCSSInWordPress, 1000);
        });
    </script>
    
    <?php wp_footer(); ?>
</body>
</html>
