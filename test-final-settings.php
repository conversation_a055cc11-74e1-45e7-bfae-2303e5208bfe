<?php
/**
 * Final Test - Direct WordPress Settings Page
 */

// WordPress environment
if (file_exists('../../../wp-config.php')) {
    require_once('../../../wp-config.php');
    require_once('../../../wp-load.php');
} else {
    die('WordPress not found');
}

if (!current_user_can('manage_options')) {
    die('Access denied');
}

// Force clear all caches
wp_cache_flush();
if (function_exists('wp_cache_flush_group')) {
    wp_cache_flush_group('setia');
}

// Update asset version
update_option('setia_asset_version', time());

// Clear any transients
global $wpdb;
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_setia_%'");

// Force opcache clear if available
if (function_exists('opcache_reset')) {
    opcache_reset();
}

?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SETIA Final Test - Redirecting...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 15px;
            padding: 40px;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0078d4;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .success-box {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
            text-align: right;
        }
        .info-item {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #0078d4;
        }
        .countdown {
            font-size: 24px;
            font-weight: bold;
            color: #0078d4;
            margin: 20px 0;
        }
        .btn {
            background: #0078d4;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .btn:hover {
            background: #106ebe;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,120,212,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 SETIA Final Test</h1>
        <div class="spinner"></div>
        
        <div class="success-box">
            <h2>✅ مشکل Hook حل شد!</h2>
            <p>URL encoding مشکل برطرف شده و شرایط تشخیص بهبود یافته</p>
        </div>

        <div class="info-grid">
            <div class="info-item">
                <strong>زمان تست:</strong><br>
                <?php echo date('Y-m-d H:i:s'); ?>
            </div>
            <div class="info-item">
                <strong>Asset Version:</strong><br>
                <?php echo get_option('setia_asset_version'); ?>
            </div>
            <div class="info-item">
                <strong>WordPress:</strong><br>
                <?php echo get_bloginfo('version'); ?>
            </div>
            <div class="info-item">
                <strong>PHP:</strong><br>
                <?php echo PHP_VERSION; ?>
            </div>
        </div>

        <div class="countdown" id="countdown">انتقال در 5 ثانیه...</div>
        
        <p>در حال انتقال به صفحه تنظیمات SETIA با تمام بهبودها...</p>
        
        <?php
        $settings_url = admin_url('admin.php?page=setia-settings&setia_debug=1&hook_fixed=1&nocache=' . time());
        ?>
        
        <div style="margin: 30px 0;">
            <a href="<?php echo esc_url($settings_url); ?>" class="btn">🔗 رفتن به تنظیمات SETIA</a>
            <a href="debug-settings.php" class="btn" target="_blank">🔍 گزارش Debug</a>
        </div>

        <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: right;">
            <h3>🔧 تغییرات اعمال شده:</h3>
            <ul style="text-align: right; margin: 0;">
                <li>✅ رفع مشکل URL encoding در hook name</li>
                <li>✅ بهبود شرایط تشخیص صفحه تنظیمات</li>
                <li>✅ اضافه کردن fallback CSS inline</li>
                <li>✅ بهبود debug و logging</li>
                <li>✅ Force cache clearing</li>
                <li>✅ Asset versioning بهبود یافته</li>
            </ul>
        </div>

        <p style="font-size: 12px; color: #666; margin-top: 30px;">
            اگر انتقال خودکار کار نکرد، روی دکمه بالا کلیک کنید
        </p>
    </div>

    <script>
        console.log('🚀 SETIA Final Test - Starting...');
        
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(function() {
            countdownElement.textContent = `انتقال در ${countdown} ثانیه...`;
            countdown--;
            
            if (countdown < 0) {
                clearInterval(timer);
                countdownElement.textContent = 'در حال انتقال...';
                countdownElement.style.color = '#10b981';
                
                console.log('🚀 Redirecting to SETIA Settings...');
                window.location.href = '<?php echo esc_js($settings_url); ?>';
            }
        }, 1000);
        
        // Log technical details
        console.log('📊 Technical Details:');
        console.log('  - WordPress Version:', '<?php echo get_bloginfo("version"); ?>');
        console.log('  - PHP Version:', '<?php echo PHP_VERSION; ?>');
        console.log('  - Asset Version:', '<?php echo get_option("setia_asset_version"); ?>');
        console.log('  - Target URL:', '<?php echo esc_js($settings_url); ?>');
    </script>
</body>
</html>
