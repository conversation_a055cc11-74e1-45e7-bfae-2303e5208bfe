<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست سرور ریموت SETIA - tickfile.ir</title>
    <style>
        body {
            font-family: '<PERSON>azi<PERSON>', '<PERSON><PERSON><PERSON>', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #2271b1;
            text-align: center;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .server-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
            border: 2px solid #2196f3;
        }
        
        .server-url {
            font-weight: bold;
            color: #1976d2;
            font-size: 18px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .test-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .test-card h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .test-button {
            background: linear-gradient(45deg, #2271b1, #135e96);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s;
            width: 100%;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(34, 113, 177, 0.3);
        }
        
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .test-button.loading {
            background: #6c757d;
            cursor: wait;
        }
        
        .test-button.loading::after {
            content: ' ⏳';
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-testing { background: #17a2b8; animation: pulse 1s infinite; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 6px;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .metrics-dashboard {
            background: #343a40;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .metric-item {
            text-align: center;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #17a2b8;
        }
        
        .metric-label {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        .results-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
        
        .clear-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin-bottom: 10px;
        }
        
        .clear-button:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 تست سرور ریموت SETIA</h1>
        
        <div class="server-info">
            <div class="server-url">🔗 https://tickfile.ir/wp-content/plugins/SETIA/</div>
            <div style="margin-top: 10px;">
                <span>وضعیت سرور:</span>
                <span class="status-indicator status-testing" id="server-status"></span>
                <span id="server-status-text">در حال بررسی...</span>
            </div>
        </div>

        <div class="metrics-dashboard">
            <h3>📊 آمار عملکرد سرور</h3>
            <div class="metrics-grid">
                <div class="metric-item">
                    <div class="metric-value" id="total-tests">0</div>
                    <div class="metric-label">کل تست‌ها</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="successful-tests">0</div>
                    <div class="metric-label">تست‌های موفق</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="failed-tests">0</div>
                    <div class="metric-label">تست‌های ناموفق</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="avg-response">0ms</div>
                    <div class="metric-label">میانگین پاسخ</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="server-load">نامشخص</div>
                    <div class="metric-label">بار سرور</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="uptime">نامشخص</div>
                    <div class="metric-label">آپتایم</div>
                </div>
            </div>
        </div>

        <div class="test-grid">
            <div class="test-card">
                <h3>🔌 تست اتصال پایه</h3>
                <button class="test-button" onclick="testBasicConnection()">تست اتصال</button>
                <button class="test-button" onclick="testWordPressAPI()">تست WordPress API</button>
                <button class="test-button" onclick="testPluginFiles()">تست فایل‌های پلاگین</button>
            </div>

            <div class="test-card">
                <h3>⚡ تست عملکرد AJAX</h3>
                <button class="test-button" onclick="testAjaxOptimized()">تست AJAX بهینه‌سازی شده</button>
                <button class="test-button" onclick="testAjaxSpeed()">تست سرعت AJAX</button>
                <button class="test-button" onclick="testConcurrentRequests()">تست درخواست‌های همزمان</button>
            </div>

            <div class="test-card">
                <h3>🔒 تست امنیت</h3>
                <button class="test-button" onclick="testNonceSecurity()">تست امنیت Nonce</button>
                <button class="test-button" onclick="testCSRFProtection()">تست محافظت CSRF</button>
                <button class="test-button" onclick="testPermissions()">تست مجوزها</button>
            </div>

            <div class="test-card">
                <h3>🛠️ تست قابلیت‌ها</h3>
                <button class="test-button" onclick="testContentGeneration()">تست تولید محتوا</button>
                <button class="test-button" onclick="testImageGeneration()">تست تولید تصویر</button>
                <button class="test-button" onclick="testHistorySystem()">تست سیستم تاریخچه</button>
            </div>
        </div>

        <div class="test-card">
            <h3>📋 نتایج تست‌ها</h3>
            <button class="clear-button" onclick="clearResults()">پاک کردن نتایج</button>
            <div class="results-container" id="results"></div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // تنظیمات سرور ریموت
        const serverConfig = {
            baseUrl: 'https://tickfile.ir',
            pluginUrl: 'https://tickfile.ir/wp-content/plugins/SETIA/',
            ajaxUrl: 'https://tickfile.ir/wp-admin/admin-ajax.php',
            timeout: 30000
        };

        // آمار تست‌ها
        let testStats = {
            total: 0,
            successful: 0,
            failed: 0,
            responseTimes: []
        };

        // تابع کمکی برای نمایش نتایج
        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('fa-IR');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            document.getElementById('results').appendChild(resultDiv);
            
            // اسکرول به پایین
            const container = document.getElementById('results');
            container.scrollTop = container.scrollHeight;
        }

        // بروزرسانی آمار
        function updateStats() {
            document.getElementById('total-tests').textContent = testStats.total;
            document.getElementById('successful-tests').textContent = testStats.successful;
            document.getElementById('failed-tests').textContent = testStats.failed;
            
            if (testStats.responseTimes.length > 0) {
                const avgTime = testStats.responseTimes.reduce((a, b) => a + b, 0) / testStats.responseTimes.length;
                document.getElementById('avg-response').textContent = Math.round(avgTime) + 'ms';
            }
        }

        // تست اتصال پایه
        async function testBasicConnection() {
            const button = event.target;
            button.classList.add('loading');
            button.disabled = true;
            
            try {
                testStats.total++;
                addResult('🔍 شروع تست اتصال پایه به سرور...', 'info');
                
                const startTime = performance.now();
                
                // تست دسترسی به سایت اصلی
                const response = await fetch(serverConfig.baseUrl, {
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                
                const endTime = performance.now();
                const responseTime = endTime - startTime;
                testStats.responseTimes.push(responseTime);
                
                testStats.successful++;
                addResult(`✅ اتصال به سرور موفق - زمان پاسخ: ${Math.round(responseTime)}ms`, 'success');
                
                // بررسی وضعیت سرور
                document.getElementById('server-status').className = 'status-indicator status-online';
                document.getElementById('server-status-text').textContent = 'آنلاین';
                
            } catch (error) {
                testStats.failed++;
                addResult(`❌ خطا در اتصال به سرور: ${error.message}`, 'error');
                
                document.getElementById('server-status').className = 'status-indicator status-offline';
                document.getElementById('server-status-text').textContent = 'آفلاین';
            } finally {
                button.classList.remove('loading');
                button.disabled = false;
                updateStats();
            }
        }

        // پاک کردن نتایج
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testStats = {
                total: 0,
                successful: 0,
                failed: 0,
                responseTimes: []
            };
            updateStats();
            addResult('نتایج پاک شدند', 'info');
        }

        // بروزرسانی خودکار آمار
        setInterval(updateStats, 5000);
        
        // تست WordPress API
        async function testWordPressAPI() {
            const button = event.target;
            button.classList.add('loading');
            button.disabled = true;

            try {
                testStats.total++;
                addResult('🔍 تست WordPress API...', 'info');

                const startTime = performance.now();

                // تست دسترسی به admin-ajax.php
                const response = await fetch(serverConfig.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=heartbeat'
                });

                const endTime = performance.now();
                const responseTime = endTime - startTime;
                testStats.responseTimes.push(responseTime);

                if (response.ok) {
                    testStats.successful++;
                    addResult(`✅ WordPress API در دسترس - زمان پاسخ: ${Math.round(responseTime)}ms`, 'success');
                } else {
                    testStats.failed++;
                    addResult(`⚠️ WordPress API پاسخ غیرمعمول داد - کد: ${response.status}`, 'warning');
                }

            } catch (error) {
                testStats.failed++;
                addResult(`❌ خطا در تست WordPress API: ${error.message}`, 'error');
            } finally {
                button.classList.remove('loading');
                button.disabled = false;
                updateStats();
            }
        }

        // تست فایل‌های پلاگین
        async function testPluginFiles() {
            const button = event.target;
            button.classList.add('loading');
            button.disabled = true;

            try {
                testStats.total++;
                addResult('🔍 تست دسترسی به فایل‌های پلاگین...', 'info');

                const filesToTest = [
                    'assets/js/ajax-optimized.js',
                    'assets/css/admin.css',
                    'assets/css/setia-fixed.css'
                ];

                let successCount = 0;

                for (const file of filesToTest) {
                    try {
                        const startTime = performance.now();
                        const response = await fetch(serverConfig.pluginUrl + file, {
                            method: 'HEAD'
                        });
                        const endTime = performance.now();
                        const responseTime = endTime - startTime;

                        if (response.ok) {
                            successCount++;
                            addResult(`✅ فایل ${file} در دسترس - ${Math.round(responseTime)}ms`, 'success');
                        } else {
                            addResult(`❌ فایل ${file} در دسترس نیست - کد: ${response.status}`, 'error');
                        }
                    } catch (error) {
                        addResult(`❌ خطا در دسترسی به ${file}: ${error.message}`, 'error');
                    }
                }

                if (successCount === filesToTest.length) {
                    testStats.successful++;
                    addResult(`✅ همه فایل‌های پلاگین در دسترس هستند (${successCount}/${filesToTest.length})`, 'success');
                } else {
                    testStats.failed++;
                    addResult(`⚠️ برخی فایل‌های پلاگین در دسترس نیستند (${successCount}/${filesToTest.length})`, 'warning');
                }

            } catch (error) {
                testStats.failed++;
                addResult(`❌ خطا در تست فایل‌های پلاگین: ${error.message}`, 'error');
            } finally {
                button.classList.remove('loading');
                button.disabled = false;
                updateStats();
            }
        }

        // تست AJAX بهینه‌سازی شده
        async function testAjaxOptimized() {
            const button = event.target;
            button.classList.add('loading');
            button.disabled = true;

            try {
                testStats.total++;
                addResult('🔍 تست سیستم AJAX بهینه‌سازی شده...', 'info');

                // بارگذاری فایل AJAX بهینه‌سازی شده
                const script = document.createElement('script');
                script.src = serverConfig.pluginUrl + 'assets/js/ajax-optimized.js';

                script.onload = function() {
                    addResult('✅ فایل AJAX بهینه‌سازی شده بارگذاری شد', 'success');

                    // تست عملکرد SetiaAjaxManager
                    if (typeof window.SetiaAjax !== 'undefined') {
                        testStats.successful++;
                        addResult('✅ SetiaAjaxManager آماده است', 'success');

                        // تست متریک‌ها
                        const metrics = window.SetiaAjax.getMetrics();
                        addResult(`📊 آمار AJAX: ${JSON.stringify(metrics)}`, 'info');
                    } else {
                        testStats.failed++;
                        addResult('❌ SetiaAjaxManager در دسترس نیست', 'error');
                    }
                };

                script.onerror = function() {
                    testStats.failed++;
                    addResult('❌ خطا در بارگذاری فایل AJAX بهینه‌سازی شده', 'error');
                };

                document.head.appendChild(script);

            } catch (error) {
                testStats.failed++;
                addResult(`❌ خطا در تست AJAX بهینه‌سازی شده: ${error.message}`, 'error');
            } finally {
                button.classList.remove('loading');
                button.disabled = false;
                updateStats();
            }
        }

        // تست اولیه هنگام بارگذاری صفحه
        window.addEventListener('load', function() {
            setTimeout(testBasicConnection, 1000);
        });
    </script>
</body>
</html>
